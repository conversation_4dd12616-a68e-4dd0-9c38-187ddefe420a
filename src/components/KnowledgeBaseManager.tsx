"use client"

import * as React from "react"
import { useState, useEffect } from "react"
import { 
  FileText, 
  Plus, 
  Search, 
  ExternalLink, 
  RefreshCw, 
  Trash2, 
  Eye, 
  Calendar,
  Hash,
  CheckCircle,
  AlertCircle,
  Clock,
  Download,
  Upload
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Progress } from "@/components/ui/progress"
import { cn } from "@/lib/utils"

interface KnowledgeItem {
  id: string
  title: string
  url: string
  lastIndexed: Date
  status: "indexed" | "failed" | "pending"
  wordCount: number
  tags: string[]
  content?: string
}

interface KnowledgeBaseManagerProps {
  className?: string
}

const KnowledgeBaseManager: React.FC<KnowledgeBaseManagerProps> = ({ className }) => {
  const [activeTab, setActiveTab] = useState<"add" | "manage" | "search">("add")
  const [knowledgeItems, setKnowledgeItems] = useState<KnowledgeItem[]>([
    {
      id: "1",
      title: "API Documentation - Authentication",
      url: "https://confluence.company.com/api/auth",
      lastIndexed: new Date(Date.now() - 86400000),
      status: "indexed",
      wordCount: 1250,
      tags: ["API", "Authentication", "Security"]
    },
    {
      id: "2", 
      title: "Service Deployment Guide",
      url: "https://confluence.company.com/deployment/guide",
      lastIndexed: new Date(Date.now() - *********),
      status: "indexed",
      wordCount: 2100,
      tags: ["Deployment", "DevOps", "Guide"]
    },
    {
      id: "3",
      title: "Database Schema Documentation",
      url: "https://confluence.company.com/db/schema",
      lastIndexed: new Date(Date.now() - *********),
      status: "failed",
      wordCount: 0,
      tags: ["Database", "Schema"]
    }
  ])

  const [newUrl, setNewUrl] = useState("")
  const [bulkUrls, setBulkUrls] = useState("")
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedItems, setSelectedItems] = useState<string[]>([])
  const [isIndexing, setIsIndexing] = useState(false)

  const handleAddUrl = async () => {
    if (!newUrl.trim()) return

    const newItem: KnowledgeItem = {
      id: Date.now().toString(),
      title: "Loading...",
      url: newUrl,
      lastIndexed: new Date(),
      status: "pending",
      wordCount: 0,
      tags: []
    }

    setKnowledgeItems(prev => [newItem, ...prev])
    setNewUrl("")
    
    // Simulate indexing process
    setTimeout(() => {
      setKnowledgeItems(prev => 
        prev.map(item => 
          item.id === newItem.id 
            ? { ...item, title: "New Knowledge Item", status: "indexed" as const, wordCount: 850, tags: ["New"] }
            : item
        )
      )
    }, 2000)
  }

  const handleBulkImport = async () => {
    const urls = bulkUrls.split('\n').filter(url => url.trim())
    if (urls.length === 0) return

    setIsIndexing(true)
    
    urls.forEach((url, index) => {
      const newItem: KnowledgeItem = {
        id: `bulk-${Date.now()}-${index}`,
        title: "Loading...",
        url: url.trim(),
        lastIndexed: new Date(),
        status: "pending",
        wordCount: 0,
        tags: []
      }
      
      setKnowledgeItems(prev => [newItem, ...prev])
    })

    setBulkUrls("")
    setIsIndexing(false)
  }

  const handleReindex = (id: string) => {
    setKnowledgeItems(prev =>
      prev.map(item =>
        item.id === id ? { ...item, status: "pending" as const, lastIndexed: new Date() } : item
      )
    )

    // Simulate reindexing
    setTimeout(() => {
      setKnowledgeItems(prev =>
        prev.map(item =>
          item.id === id ? { ...item, status: "indexed" as const, wordCount: Math.floor(Math.random() * 2000) + 500 } : item
        )
      )
    }, 1500)
  }

  const handleDelete = (id: string) => {
    setKnowledgeItems(prev => prev.filter(item => item.id !== id))
    setSelectedItems(prev => prev.filter(itemId => itemId !== id))
  }

  const handleBulkDelete = () => {
    setKnowledgeItems(prev => prev.filter(item => !selectedItems.includes(item.id)))
    setSelectedItems([])
  }

  const toggleSelection = (id: string) => {
    setSelectedItems(prev =>
      prev.includes(id) ? prev.filter(itemId => itemId !== id) : [...prev, id]
    )
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "indexed": return <CheckCircle className="h-4 w-4 text-green-500" />
      case "failed": return <AlertCircle className="h-4 w-4 text-red-500" />
      case "pending": return <Clock className="h-4 w-4 text-yellow-500" />
      default: return <AlertCircle className="h-4 w-4 text-gray-500" />
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "indexed": return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Indexed</Badge>
      case "failed": return <Badge variant="destructive">Failed</Badge>
      case "pending": return <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">Pending</Badge>
      default: return <Badge variant="outline">Unknown</Badge>
    }
  }

  const filteredItems = knowledgeItems.filter(item =>
    item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    item.url.toLowerCase().includes(searchQuery.toLowerCase()) ||
    item.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
  )

  return (
    <div className={cn("w-full h-full", className)}>
      <div className="flex h-full">
        {/* Sidebar Navigation */}
        <div className="w-64 border-r border-border bg-muted/30 p-4">
          <div className="space-y-2">
            <h2 className="text-lg font-semibold mb-4 flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Knowledge Base
            </h2>
            
            <Button
              variant={activeTab === "add" ? "default" : "ghost"}
              className="w-full justify-start"
              onClick={() => setActiveTab("add")}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Content
            </Button>
            
            <Button
              variant={activeTab === "manage" ? "default" : "ghost"}
              className="w-full justify-start"
              onClick={() => setActiveTab("manage")}
            >
              <FileText className="h-4 w-4 mr-2" />
              Manage Content
            </Button>
            
            <Button
              variant={activeTab === "search" ? "default" : "ghost"}
              className="w-full justify-start"
              onClick={() => setActiveTab("search")}
            >
              <Search className="h-4 w-4 mr-2" />
              Search Knowledge
            </Button>
          </div>

          <Separator className="my-6" />

          <div className="space-y-3">
            <h3 className="text-sm font-medium text-muted-foreground">Statistics</h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>Total Items:</span>
                <span className="font-medium">{knowledgeItems.length}</span>
              </div>
              <div className="flex justify-between">
                <span>Indexed:</span>
                <span className="font-medium text-green-600">
                  {knowledgeItems.filter(item => item.status === "indexed").length}
                </span>
              </div>
              <div className="flex justify-between">
                <span>Failed:</span>
                <span className="font-medium text-red-600">
                  {knowledgeItems.filter(item => item.status === "failed").length}
                </span>
              </div>
              <div className="flex justify-between">
                <span>Pending:</span>
                <span className="font-medium text-yellow-600">
                  {knowledgeItems.filter(item => item.status === "pending").length}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 p-6 overflow-y-auto">
          {activeTab === "add" && (
            <div className="space-y-6">
              <div>
                <h3 className="text-xl font-semibold mb-2">Add New Content</h3>
                <p className="text-muted-foreground">Index new Confluence pages or documentation</p>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Single URL</CardTitle>
                  <CardDescription>Add a single Confluence page or document</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex gap-2">
                    <Input
                      placeholder="https://confluence.company.com/page-url"
                      value={newUrl}
                      onChange={(e) => setNewUrl(e.target.value)}
                      className="flex-1"
                    />
                    <Button onClick={handleAddUrl} disabled={!newUrl.trim()}>
                      <Plus className="h-4 w-4 mr-2" />
                      Add URL
                    </Button>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Bulk Import</CardTitle>
                  <CardDescription>Import multiple URLs at once (one per line)</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Textarea
                    placeholder="https://confluence.company.com/page1&#10;https://confluence.company.com/page2&#10;https://confluence.company.com/page3"
                    value={bulkUrls}
                    onChange={(e) => setBulkUrls(e.target.value)}
                    rows={6}
                  />
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">
                      {bulkUrls.split('\n').filter(url => url.trim()).length} URLs ready to import
                    </span>
                    <Button onClick={handleBulkImport} disabled={!bulkUrls.trim() || isIndexing}>
                      {isIndexing ? (
                        <>
                          <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                          Indexing...
                        </>
                      ) : (
                        <>
                          <Upload className="h-4 w-4 mr-2" />
                          Bulk Import
                        </>
                      )}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {activeTab === "manage" && (
            <div className="space-y-6">
              <div className="flex justify-between items-center">
                <div>
                  <h3 className="text-xl font-semibold mb-2">Manage Content</h3>
                  <p className="text-muted-foreground">View and manage your indexed knowledge base</p>
                </div>
                {selectedItems.length > 0 && (
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm">
                      <RefreshCw className="h-4 w-4 mr-2" />
                      Reindex Selected ({selectedItems.length})
                    </Button>
                    <Button variant="destructive" size="sm" onClick={handleBulkDelete}>
                      <Trash2 className="h-4 w-4 mr-2" />
                      Delete Selected ({selectedItems.length})
                    </Button>
                  </div>
                )}
              </div>

              <div className="flex gap-2 mb-4">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search by title, URL, or tags..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
                <Button variant="outline">
                  <Download className="h-4 w-4 mr-2" />
                  Export
                </Button>
              </div>

              <div className="space-y-3">
                {filteredItems.map((item) => (
                  <Card key={item.id} className="hover:shadow-md transition-shadow">
                    <CardContent className="p-4">
                      <div className="flex items-start gap-4">
                        <input
                          type="checkbox"
                          checked={selectedItems.includes(item.id)}
                          onChange={() => toggleSelection(item.id)}
                          className="mt-1"
                        />
                        
                        <div className="flex-1 space-y-2">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-1">
                                {getStatusIcon(item.status)}
                                <h4 className="font-medium">{item.title}</h4>
                                {getStatusBadge(item.status)}
                              </div>
                              <a
                                href={item.url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-sm text-blue-600 hover:underline flex items-center gap-1"
                              >
                                {item.url}
                                <ExternalLink className="h-3 w-3" />
                              </a>
                            </div>
                            
                            <div className="flex gap-1">
                              <Button variant="ghost" size="sm">
                                <Eye className="h-4 w-4" />
                              </Button>
                              <Button variant="ghost" size="sm" onClick={() => handleReindex(item.id)}>
                                <RefreshCw className="h-4 w-4" />
                              </Button>
                              <Button variant="ghost" size="sm" onClick={() => handleDelete(item.id)}>
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>

                          <div className="flex items-center gap-4 text-xs text-muted-foreground">
                            <div className="flex items-center gap-1">
                              <Calendar className="h-3 w-3" />
                              {item.lastIndexed.toLocaleDateString()}
                            </div>
                            <div className="flex items-center gap-1">
                              <Hash className="h-3 w-3" />
                              {item.wordCount.toLocaleString()} words
                            </div>
                          </div>

                          {item.tags.length > 0 && (
                            <div className="flex gap-1 flex-wrap">
                              {item.tags.map((tag) => (
                                <Badge key={tag} variant="secondary" className="text-xs">
                                  {tag}
                                </Badge>
                              ))}
                            </div>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          )}

          {activeTab === "search" && (
            <div className="space-y-6">
              <div>
                <h3 className="text-xl font-semibold mb-2">Search Knowledge Base</h3>
                <p className="text-muted-foreground">Find relevant information from your indexed content</p>
              </div>

              <Card>
                <CardContent className="p-6">
                  <div className="space-y-4">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder="Search for information, concepts, or specific topics..."
                        className="pl-10 text-base h-12"
                      />
                    </div>
                    
                    <div className="flex gap-2 flex-wrap">
                      <Button variant="outline" size="sm">API</Button>
                      <Button variant="outline" size="sm">Authentication</Button>
                      <Button variant="outline" size="sm">Deployment</Button>
                      <Button variant="outline" size="sm">Database</Button>
                      <Button variant="outline" size="sm">Security</Button>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <div className="space-y-4">
                <h4 className="font-medium">Search Results</h4>
                <div className="text-center py-12 text-muted-foreground">
                  <Search className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>Enter a search query to find relevant information</p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default KnowledgeBaseManager
