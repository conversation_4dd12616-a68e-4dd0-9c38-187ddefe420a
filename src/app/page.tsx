'use client';

import { useState, useEffect } from 'react';
import { MissionDashboard } from '@/components/MissionDashboard';
import { ConfluencePanel } from '@/components/ConfluencePanel';
import { CreateMissionDialog } from '@/components/CreateMissionDialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
// import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import {
  Bot,
  FileText,
  Target,
  Zap,
  Clock,
  CheckCircle2,
  AlertTriangle,
  XCircle,
  Calendar,
  BarChart,
  Filter,
  Search
} from 'lucide-react';
import { Input } from '@/components/ui/input';

interface Mission {
  id: string;
  title: string;
  description: string;
  status: 'pending' | 'planning' | 'executing' | 'completed' | 'failed';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  createdAt: string;
  updatedAt: string;
  completedAt?: string;
}

export default function Home() {
  const [missions, setMissions] = useState<Mission[]>([]);
  const [selectedMission, setSelectedMission] = useState<Mission | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [activeFilter, setActiveFilter] = useState('all');

  useEffect(() => {
    fetchMissions();
  }, []);

  const fetchMissions = async () => {
    try {
      const response = await fetch('/api/missions');
      const result = await response.json();
      if (result.success) {
        setMissions(result.data);
      }
    } catch (error) {
      console.error('Failed to fetch missions:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateMission = async (missionData: { title: string; description: string; priority: string }) => {
    try {
      const response = await fetch('/api/missions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(missionData),
      });

      const result = await response.json();
      if (result.success) {
        setMissions(prev => [result.data, ...prev]);
        setIsCreateDialogOpen(false);
      }
    } catch (error) {
      console.error('Failed to create mission:', error);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle2 className="h-4 w-4 text-green-500" />;
      case 'executing': return <Zap className="h-4 w-4 text-blue-500" />;
      case 'planning': return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'failed': return <XCircle className="h-4 w-4 text-red-500" />;
      default: return <AlertTriangle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-500';
      case 'executing': return 'bg-blue-500';
      case 'planning': return 'bg-yellow-500';
      case 'failed': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const getStatusProgress = (status: string) => {
    switch (status) {
      case 'completed': return 100;
      case 'executing': return 60;
      case 'planning': return 30;
      case 'failed': return 100;
      default: return 10;
    }
  };

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return <Badge variant="destructive" className="text-xs font-medium">Urgent</Badge>;
      case 'high':
        return <Badge variant="outline" className="bg-orange-100 text-orange-800 border-orange-200 text-xs font-medium">High</Badge>;
      case 'medium':
        return <Badge variant="outline" className="bg-blue-100 text-blue-800 border-blue-200 text-xs font-medium">Medium</Badge>;
      case 'low':
        return <Badge variant="outline" className="bg-gray-100 text-gray-800 border-gray-200 text-xs font-medium">Low</Badge>;
      default:
        return <Badge variant="outline" className="text-xs font-medium">Unknown</Badge>;
    }
  };

  const filteredMissions = missions.filter(mission => {
    const matchesSearch = mission.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                          mission.description.toLowerCase().includes(searchQuery.toLowerCase());

    if (activeFilter === 'all') return matchesSearch;
    return matchesSearch && mission.status === activeFilter;
  });

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center space-y-4">
          <Bot className="h-12 w-12 mx-auto text-primary animate-pulse" />
          <p className="text-muted-foreground">Loading Service Management Assistant...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="sticky top-0 z-10 bg-background/95 backdrop-blur-sm border-b border-border">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-3">
              <div className="bg-primary/10 p-2 rounded-lg">
                <Bot className="h-6 w-6 text-primary" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-foreground">Service Management Assistant</h1>
                <p className="text-xs text-muted-foreground">AI-powered autonomous mission execution</p>
              </div>
            </div>
            <Button onClick={() => setIsCreateDialogOpen(true)}>
              <Target className="h-4 w-4 mr-2" />
              New Mission
            </Button>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Mission List */}
          <div className="lg:col-span-2 space-y-6">
            <Card>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg flex items-center space-x-2">
                    <Target className="h-5 w-5 text-primary" />
                    <span>Mission Control</span>
                  </CardTitle>
                  <Button variant="outline" size="sm" className="h-8">
                    <Filter className="h-3.5 w-3.5 mr-1" />
                    Filter
                  </Button>
                </div>
                <CardDescription>
                  Manage and monitor your AI agent missions
                </CardDescription>

                <div className="mt-2 flex items-center gap-2">
                  <div className="relative flex-1">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search missions..."
                      className="pl-9"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>
                </div>
              </CardHeader>

              <div className="px-6">
                <div className="grid grid-cols-5 mb-4 gap-1">
                  <Button variant={activeFilter === 'all' ? 'default' : 'outline'} size="sm" onClick={() => setActiveFilter('all')}>All</Button>
                  <Button variant={activeFilter === 'planning' ? 'default' : 'outline'} size="sm" onClick={() => setActiveFilter('planning')}>Planning</Button>
                  <Button variant={activeFilter === 'executing' ? 'default' : 'outline'} size="sm" onClick={() => setActiveFilter('executing')}>Executing</Button>
                  <Button variant={activeFilter === 'completed' ? 'default' : 'outline'} size="sm" onClick={() => setActiveFilter('completed')}>Completed</Button>
                  <Button variant={activeFilter === 'failed' ? 'default' : 'outline'} size="sm" onClick={() => setActiveFilter('failed')}>Failed</Button>
                </div>

                <div className="space-y-0 mt-0">
                  {filteredMissions.length === 0 ? (
                    <div className="text-center py-12">
                      <Target className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                      <p className="text-muted-foreground">No missions found. Create your first mission to get started.</p>
                    </div>
                  ) : (
                    <div className="space-y-3 pb-4">
                      {filteredMissions.map((mission) => (
                        <div
                          key={mission.id}
                          className={`p-4 border rounded-lg cursor-pointer transition-all hover:shadow-md ${
                            selectedMission?.id === mission.id
                              ? 'border-primary bg-primary/5'
                              : 'border-border hover:border-primary/30'
                          }`}
                          onClick={() => setSelectedMission(mission)}
                        >
                          <div className="flex items-start justify-between gap-4">
                            <div className="flex-1 space-y-2">
                              <div className="flex items-center gap-2">
                                {getStatusIcon(mission.status)}
                                <h3 className="font-medium text-foreground">{mission.title}</h3>
                                {getPriorityBadge(mission.priority)}
                              </div>

                              <p className="text-sm text-muted-foreground line-clamp-2">{mission.description}</p>

                              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                                <div className="flex items-center">
                                  <Calendar className="h-3 w-3 mr-1" />
                                  {new Date(mission.createdAt).toLocaleDateString()}
                                </div>
                                <Separator orientation="vertical" className="h-3" />
                                <div className="flex items-center">
                                  <Clock className="h-3 w-3 mr-1" />
                                  {new Date(mission.updatedAt).toLocaleTimeString()}
                                </div>
                              </div>

                              <div className="flex items-center gap-2">
                                <Progress value={getStatusProgress(mission.status)} className="h-1.5" />
                                <span className="text-xs font-medium capitalize text-muted-foreground">{mission.status}</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </Card>


            {/* Mission Details */}
            {selectedMission && (
              <Card className="overflow-hidden">
                <CardHeader className="bg-muted/50 pb-3">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg flex items-center gap-2">
                      <div className={`w-2 h-2 rounded-full ${getStatusColor(selectedMission.status)}`} />
                      <span>{selectedMission.title}</span>
                    </CardTitle>
                    {getPriorityBadge(selectedMission.priority)}
                  </div>
                  <CardDescription className="line-clamp-2">
                    {selectedMission.description}
                  </CardDescription>
                </CardHeader>
                <CardContent className="pt-6">
                  <MissionDashboard mission={selectedMission} />
                </CardContent>
                <CardFooter className="bg-muted/30 border-t px-6 py-3">
                  <div className="flex items-center justify-between w-full text-xs text-muted-foreground">
                    <div className="flex items-center gap-4">
                      <div className="flex items-center">
                        <Calendar className="h-3.5 w-3.5 mr-1.5" />
                        Created: {new Date(selectedMission.createdAt).toLocaleDateString()}
                      </div>
                      <div className="flex items-center">
                        <Clock className="h-3.5 w-3.5 mr-1.5" />
                        Updated: {new Date(selectedMission.updatedAt).toLocaleDateString()}
                      </div>
                    </div>
                    <div className="flex items-center">
                      <BarChart className="h-3.5 w-3.5 mr-1.5" />
                      Progress: {getStatusProgress(selectedMission.status)}%
                    </div>
                  </div>
                </CardFooter>
              </Card>
            )}
          </div>

          {/* Confluence Panel */}
          <div>
            <Card className="h-full">
              <CardHeader className="pb-3">
                <CardTitle className="text-lg flex items-center space-x-2">
                  <FileText className="h-5 w-5 text-primary" />
                  <span>Knowledge Base</span>
                </CardTitle>
                <CardDescription>
                  Access Confluence resources and documentation
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ConfluencePanel />
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Create Mission Dialog */}
      <CreateMissionDialog
        open={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
        onSubmit={handleCreateMission}
      />
    </div>
  );
}
