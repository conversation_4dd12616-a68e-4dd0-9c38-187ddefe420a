'use client';

import { useState, useEffect } from 'react';
import { MissionDashboard } from '@/components/MissionDashboard';
import { ConfluencePanel } from '@/components/ConfluencePanel';
import { CreateMissionDialog } from '@/components/CreateMissionDialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Bot, FileText, Target, Zap } from 'lucide-react';

interface Mission {
  id: string;
  title: string;
  description: string;
  status: 'pending' | 'planning' | 'executing' | 'completed' | 'failed';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  createdAt: string;
  updatedAt: string;
  completedAt?: string;
}

export default function Home() {
  const [missions, setMissions] = useState<Mission[]>([]);
  const [selectedMission, setSelectedMission] = useState<Mission | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchMissions();
  }, []);

  const fetchMissions = async () => {
    try {
      const response = await fetch('/api/missions');
      const result = await response.json();
      if (result.success) {
        setMissions(result.data);
      }
    } catch (error) {
      console.error('Failed to fetch missions:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateMission = async (missionData: { title: string; description: string; priority: string }) => {
    try {
      const response = await fetch('/api/missions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(missionData),
      });

      const result = await response.json();
      if (result.success) {
        setMissions(prev => [result.data, ...prev]);
        setIsCreateDialogOpen(false);
      }
    } catch (error) {
      console.error('Failed to create mission:', error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-500';
      case 'executing': return 'bg-blue-500';
      case 'planning': return 'bg-yellow-500';
      case 'failed': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'bg-red-100 text-red-800';
      case 'high': return 'bg-orange-100 text-orange-800';
      case 'medium': return 'bg-blue-100 text-blue-800';
      case 'low': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <Bot className="h-12 w-12 mx-auto mb-4 animate-pulse" />
          <p>Loading Service Management Assistant...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center space-x-3">
              <Bot className="h-8 w-8 text-blue-600" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Service Management Assistant</h1>
                <p className="text-sm text-gray-500">AI-powered autonomous mission execution</p>
              </div>
            </div>
            <Button onClick={() => setIsCreateDialogOpen(true)}>
              <Target className="h-4 w-4 mr-2" />
              New Mission
            </Button>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Mission List */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Target className="h-5 w-5" />
                  <span>Active Missions</span>
                </CardTitle>
                <CardDescription>
                  Manage and monitor your AI agent missions
                </CardDescription>
              </CardHeader>
              <CardContent>
                {missions.length === 0 ? (
                  <div className="text-center py-8">
                    <Target className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                    <p className="text-gray-500">No missions yet. Create your first mission to get started.</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {missions.map((mission) => (
                      <div
                        key={mission.id}
                        className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                          selectedMission?.id === mission.id
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                        onClick={() => setSelectedMission(mission)}
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <h3 className="font-semibold text-gray-900">{mission.title}</h3>
                            <p className="text-sm text-gray-600 mt-1">{mission.description}</p>
                            <div className="flex items-center space-x-2 mt-2">
                              <div className={`w-2 h-2 rounded-full ${getStatusColor(mission.status)}`} />
                              <span className="text-xs text-gray-500 capitalize">{mission.status}</span>
                              <Badge className={getPriorityColor(mission.priority)}>
                                {mission.priority}
                              </Badge>
                            </div>
                          </div>
                          <div className="text-xs text-gray-400">
                            {new Date(mission.createdAt).toLocaleDateString()}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Confluence Panel */}
          <div>
            <ConfluencePanel />
          </div>
        </div>

        {/* Mission Details */}
        {selectedMission && (
          <div className="mt-8">
            <MissionDashboard mission={selectedMission} />
          </div>
        )}
      </div>

      {/* Create Mission Dialog */}
      <CreateMissionDialog
        open={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
        onSubmit={handleCreateMission}
      />
    </div>
  );
}
