{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/Node/my-assistant-v3/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/Node/my-assistant-v3/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/Node/my-assistant-v3/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS", "debugId": null}}, {"offset": {"line": 120, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/Node/my-assistant-v3/src/components/ui/chat-interface.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { useState, useCallback, useEffect, useRef, FormEvent } from \"react\"\nimport { ArrowDown, Paperclip, Mic, CornerDownLeft, MessageSquare, Plus, Settings, User, History, Search } from \"lucide-react\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Textarea } from \"@/components/ui/textarea\"\nimport { cn } from \"@/lib/utils\"\n\n// Auto-scroll hook\ninterface ScrollState {\n  isAtBottom: boolean\n  autoScrollEnabled: boolean\n}\n\ninterface UseAutoScrollOptions {\n  offset?: number\n  smooth?: boolean\n  content?: React.ReactNode\n}\n\nfunction useAutoScroll(options: UseAutoScrollOptions = {}) {\n  const { offset = 20, smooth = false, content } = options\n  const scrollRef = useRef<HTMLDivElement>(null)\n  const lastContentHeight = useRef(0)\n  const userHasScrolled = useRef(false)\n\n  const [scrollState, setScrollState] = useState<ScrollState>({\n    isAtBottom: true,\n    autoScrollEnabled: true,\n  })\n\n  const checkIsAtBottom = useCallback(\n    (element: HTMLElement) => {\n      const { scrollTop, scrollHeight, clientHeight } = element\n      const distanceToBottom = Math.abs(\n        scrollHeight - scrollTop - clientHeight\n      )\n      return distanceToBottom <= offset\n    },\n    [offset]\n  )\n\n  const scrollToBottom = useCallback(\n    (instant?: boolean) => {\n      if (!scrollRef.current) return\n\n      const targetScrollTop =\n        scrollRef.current.scrollHeight - scrollRef.current.clientHeight\n\n      if (instant) {\n        scrollRef.current.scrollTop = targetScrollTop\n      } else {\n        scrollRef.current.scrollTo({\n          top: targetScrollTop,\n          behavior: smooth ? \"smooth\" : \"auto\",\n        })\n      }\n\n      setScrollState({\n        isAtBottom: true,\n        autoScrollEnabled: true,\n      })\n      userHasScrolled.current = false\n    },\n    [smooth]\n  )\n\n  const handleScroll = useCallback(() => {\n    if (!scrollRef.current) return\n\n    const atBottom = checkIsAtBottom(scrollRef.current)\n\n    setScrollState((prev) => ({\n      isAtBottom: atBottom,\n      autoScrollEnabled: atBottom ? true : prev.autoScrollEnabled,\n    }))\n  }, [checkIsAtBottom])\n\n  useEffect(() => {\n    const element = scrollRef.current\n    if (!element) return\n\n    element.addEventListener(\"scroll\", handleScroll, { passive: true })\n    return () => element.removeEventListener(\"scroll\", handleScroll)\n  }, [handleScroll])\n\n  useEffect(() => {\n    const scrollElement = scrollRef.current\n    if (!scrollElement) return\n\n    const currentHeight = scrollElement.scrollHeight\n    const hasNewContent = currentHeight !== lastContentHeight.current\n\n    if (hasNewContent) {\n      if (scrollState.autoScrollEnabled) {\n        requestAnimationFrame(() => {\n          scrollToBottom(lastContentHeight.current === 0)\n        })\n      }\n      lastContentHeight.current = currentHeight\n    }\n  }, [content, scrollState.autoScrollEnabled, scrollToBottom])\n\n  const disableAutoScroll = useCallback(() => {\n    const atBottom = scrollRef.current\n      ? checkIsAtBottom(scrollRef.current)\n      : false\n\n    if (!atBottom) {\n      userHasScrolled.current = true\n      setScrollState((prev) => ({\n        ...prev,\n        autoScrollEnabled: false,\n      }))\n    }\n  }, [checkIsAtBottom])\n\n  return {\n    scrollRef,\n    isAtBottom: scrollState.isAtBottom,\n    autoScrollEnabled: scrollState.autoScrollEnabled,\n    scrollToBottom: () => scrollToBottom(false),\n    disableAutoScroll,\n  }\n}\n\n// Chat Input Component\ninterface ChatInputProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}\n\nconst ChatInput = React.forwardRef<HTMLTextAreaElement, ChatInputProps>(\n  ({ className, ...props }, ref) => (\n    <Textarea\n      autoComplete=\"off\"\n      ref={ref}\n      name=\"message\"\n      className={cn(\n        \"max-h-12 px-4 py-3 bg-background text-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 w-full rounded-md flex items-center h-16 resize-none\",\n        className,\n      )}\n      {...props}\n    />\n  ),\n)\nChatInput.displayName = \"ChatInput\"\n\n// Chat Message List Component\ninterface ChatMessageListProps extends React.HTMLAttributes<HTMLDivElement> {\n  smooth?: boolean\n}\n\nconst ChatMessageList = React.forwardRef<HTMLDivElement, ChatMessageListProps>(\n  ({ className, children, smooth = false, ...props }, _ref) => {\n    const {\n      scrollRef,\n      isAtBottom,\n      autoScrollEnabled,\n      scrollToBottom,\n      disableAutoScroll,\n    } = useAutoScroll({\n      smooth,\n      content: children,\n    })\n\n    return (\n      <div className=\"relative w-full h-full\">\n        <div\n          className={`flex flex-col w-full h-full p-4 overflow-y-auto ${className}`}\n          ref={scrollRef}\n          onWheel={disableAutoScroll}\n          onTouchMove={disableAutoScroll}\n          {...props}\n        >\n          <div className=\"flex flex-col gap-6\">{children}</div>\n        </div>\n\n        {!isAtBottom && (\n          <Button\n            onClick={() => {\n              scrollToBottom()\n            }}\n            size=\"icon\"\n            variant=\"outline\"\n            className=\"absolute bottom-2 left-1/2 transform -translate-x-1/2 inline-flex rounded-full shadow-md\"\n            aria-label=\"Scroll to bottom\"\n          >\n            <ArrowDown className=\"h-4 w-4\" />\n          </Button>\n        )}\n      </div>\n    )\n  }\n)\n\nChatMessageList.displayName = \"ChatMessageList\"\n\n// Message Interface\ninterface Message {\n  id: number\n  content: string\n  sender: \"user\" | \"ai\"\n  timestamp: Date\n}\n\ninterface Mission {\n  id: number\n  title: string\n  timestamp: Date\n  status: \"completed\" | \"in-progress\" | \"failed\"\n}\n\nexport { ChatInput, ChatMessageList, useAutoScroll, type Message, type Mission }\n"], "names": [], "mappings": ";;;;;;AAEA;AAEA;AACA;AACA;AACA;;;AAPA;;;;;;;AAqBA,SAAS,cAAc,UAAgC,CAAC,CAAC;;IACvD,MAAM,EAAE,SAAS,EAAE,EAAE,SAAS,KAAK,EAAE,OAAO,EAAE,GAAG;IACjD,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IACzC,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACjC,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAE/B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;QAC1D,YAAY;QACZ,mBAAmB;IACrB;IAEA,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAChC,CAAC;YACC,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,YAAY,EAAE,GAAG;YAClD,MAAM,mBAAmB,KAAK,GAAG,CAC/B,eAAe,YAAY;YAE7B,OAAO,oBAAoB;QAC7B;qDACA;QAAC;KAAO;IAGV,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAC/B,CAAC;YACC,IAAI,CAAC,UAAU,OAAO,EAAE;YAExB,MAAM,kBACJ,UAAU,OAAO,CAAC,YAAY,GAAG,UAAU,OAAO,CAAC,YAAY;YAEjE,IAAI,SAAS;gBACX,UAAU,OAAO,CAAC,SAAS,GAAG;YAChC,OAAO;gBACL,UAAU,OAAO,CAAC,QAAQ,CAAC;oBACzB,KAAK;oBACL,UAAU,SAAS,WAAW;gBAChC;YACF;YAEA,eAAe;gBACb,YAAY;gBACZ,mBAAmB;YACrB;YACA,gBAAgB,OAAO,GAAG;QAC5B;oDACA;QAAC;KAAO;IAGV,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE;YAC/B,IAAI,CAAC,UAAU,OAAO,EAAE;YAExB,MAAM,WAAW,gBAAgB,UAAU,OAAO;YAElD;2DAAe,CAAC,OAAS,CAAC;wBACxB,YAAY;wBACZ,mBAAmB,WAAW,OAAO,KAAK,iBAAiB;oBAC7D,CAAC;;QACH;kDAAG;QAAC;KAAgB;IAEpB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM,UAAU,UAAU,OAAO;YACjC,IAAI,CAAC,SAAS;YAEd,QAAQ,gBAAgB,CAAC,UAAU,cAAc;gBAAE,SAAS;YAAK;YACjE;2CAAO,IAAM,QAAQ,mBAAmB,CAAC,UAAU;;QACrD;kCAAG;QAAC;KAAa;IAEjB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM,gBAAgB,UAAU,OAAO;YACvC,IAAI,CAAC,eAAe;YAEpB,MAAM,gBAAgB,cAAc,YAAY;YAChD,MAAM,gBAAgB,kBAAkB,kBAAkB,OAAO;YAEjE,IAAI,eAAe;gBACjB,IAAI,YAAY,iBAAiB,EAAE;oBACjC;mDAAsB;4BACpB,eAAe,kBAAkB,OAAO,KAAK;wBAC/C;;gBACF;gBACA,kBAAkB,OAAO,GAAG;YAC9B;QACF;kCAAG;QAAC;QAAS,YAAY,iBAAiB;QAAE;KAAe;IAE3D,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE;YACpC,MAAM,WAAW,UAAU,OAAO,GAC9B,gBAAgB,UAAU,OAAO,IACjC;YAEJ,IAAI,CAAC,UAAU;gBACb,gBAAgB,OAAO,GAAG;gBAC1B;oEAAe,CAAC,OAAS,CAAC;4BACxB,GAAG,IAAI;4BACP,mBAAmB;wBACrB,CAAC;;YACH;QACF;uDAAG;QAAC;KAAgB;IAEpB,OAAO;QACL;QACA,YAAY,YAAY,UAAU;QAClC,mBAAmB,YAAY,iBAAiB;QAChD,gBAAgB,IAAM,eAAe;QACrC;IACF;AACF;GAxGS;AA6GT,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,6LAAC,uIAAA,CAAA,WAAQ;QACP,cAAa;QACb,KAAK;QACL,MAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sOACA;QAED,GAAG,KAAK;;;;;;;AAIf,UAAU,WAAW,GAAG;AAOxB,MAAM,gCAAkB,IAAA,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,YACrC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,KAAK,EAAE,GAAG,OAAO,EAAE;;IAClD,MAAM,EACJ,SAAS,EACT,UAAU,EACV,iBAAiB,EACjB,cAAc,EACd,iBAAiB,EAClB,GAAG,cAAc;QAChB;QACA,SAAS;IACX;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,WAAW,CAAC,gDAAgD,EAAE,WAAW;gBACzE,KAAK;gBACL,SAAS;gBACT,aAAa;gBACZ,GAAG,KAAK;0BAET,cAAA,6LAAC;oBAAI,WAAU;8BAAuB;;;;;;;;;;;YAGvC,CAAC,4BACA,6LAAC,qIAAA,CAAA,SAAM;gBACL,SAAS;oBACP;gBACF;gBACA,MAAK;gBACL,SAAQ;gBACR,WAAU;gBACV,cAAW;0BAEX,cAAA,6LAAC,mNAAA,CAAA,YAAS;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAK/B;;QAhCM;;;;QAAA;;;;AAmCR,gBAAgB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 348, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/Node/my-assistant-v3/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 380, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/Node/my-assistant-v3/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 495, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/Node/my-assistant-v3/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 547, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/Node/my-assistant-v3/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Separator({\n  className,\n  orientation = \"horizontal\",\n  decorative = true,\n  ...props\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\n  return (\n    <SeparatorPrimitive.Root\n      data-slot=\"separator\"\n      decorative={decorative}\n      orientation={orientation}\n      className={cn(\n        \"bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Separator }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,YAAY,EAC1B,aAAa,IAAI,EACjB,GAAG,OACkD;IACrD,qBACE,6LAAC,wKAAA,CAAA,OAAuB;QACtB,aAAU;QACV,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;;;;;AAGf;KAlBS", "debugId": null}}, {"offset": {"line": 592, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/Node/my-assistant-v3/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { Check, ChevronRight, Circle } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst DropdownMenu = DropdownMenuPrimitive.Root\n\nconst DropdownMenuTrigger = DropdownMenuPrimitive.Trigger\n\nconst DropdownMenuGroup = DropdownMenuPrimitive.Group\n\nconst DropdownMenuPortal = DropdownMenuPrimitive.Portal\n\nconst DropdownMenuSub = DropdownMenuPrimitive.Sub\n\nconst DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup\n\nconst DropdownMenuSubTrigger = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {\n    inset?: boolean\n  }\n>(({ className, inset, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubTrigger\n    ref={ref}\n    className={cn(\n      \"flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <ChevronRight className=\"ml-auto h-4 w-4\" />\n  </DropdownMenuPrimitive.SubTrigger>\n))\nDropdownMenuSubTrigger.displayName =\n  DropdownMenuPrimitive.SubTrigger.displayName\n\nconst DropdownMenuSubContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubContent\n    ref={ref}\n    className={cn(\n      \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuSubContent.displayName =\n  DropdownMenuPrimitive.SubContent.displayName\n\nconst DropdownMenuContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>\n>(({ className, sideOffset = 4, ...props }, ref) => (\n  <DropdownMenuPrimitive.Portal>\n    <DropdownMenuPrimitive.Content\n      ref={ref}\n      sideOffset={sideOffset}\n      className={cn(\n        \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        className\n      )}\n      {...props}\n    />\n  </DropdownMenuPrimitive.Portal>\n))\nDropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName\n\nconst DropdownMenuItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName\n\nconst DropdownMenuCheckboxItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem>\n>(({ className, children, checked, ...props }, ref) => (\n  <DropdownMenuPrimitive.CheckboxItem\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    checked={checked}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.CheckboxItem>\n))\nDropdownMenuCheckboxItem.displayName =\n  DropdownMenuPrimitive.CheckboxItem.displayName\n\nconst DropdownMenuRadioItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem>\n>(({ className, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.RadioItem\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Circle className=\"h-2 w-2 fill-current\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.RadioItem>\n))\nDropdownMenuRadioItem.displayName = DropdownMenuPrimitive.RadioItem.displayName\n\nconst DropdownMenuLabel = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Label\n    ref={ref}\n    className={cn(\n      \"px-2 py-1.5 text-sm font-semibold\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName\n\nconst DropdownMenuSeparator = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nDropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName\n\nconst DropdownMenuShortcut = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLSpanElement>) => {\n  return (\n    <span\n      className={cn(\"ml-auto text-xs tracking-widest opacity-60\", className)}\n      {...props}\n    />\n  )\n}\nDropdownMenuShortcut.displayName = \"DropdownMenuShortcut\"\n\nexport {\n  DropdownMenu,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuGroup,\n  DropdownMenuPortal,\n  DropdownMenuSub,\n  DropdownMenuSubContent,\n  DropdownMenuSubTrigger,\n  DropdownMenuRadioGroup,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,eAAe,+KAAA,CAAA,OAA0B;AAE/C,MAAM,sBAAsB,+KAAA,CAAA,UAA6B;AAEzD,MAAM,oBAAoB,+KAAA,CAAA,QAA2B;AAErD,MAAM,qBAAqB,+KAAA,CAAA,SAA4B;AAEvD,MAAM,kBAAkB,+KAAA,CAAA,MAAyB;AAEjD,MAAM,yBAAyB,+KAAA,CAAA,aAAgC;AAE/D,MAAM,uCAAyB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAK5C,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAC3C,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wIACA,SAAS,QACT;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,yNAAA,CAAA,eAAY;gBAAC,WAAU;;;;;;;;;;;;;AAG5B,uBAAuB,WAAW,GAChC,+KAAA,CAAA,aAAgC,CAAC,WAAW;AAE9C,MAAM,uCAAyB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ybACA;QAED,GAAG,KAAK;;;;;;;AAGb,uBAAuB,WAAW,GAChC,+KAAA,CAAA,aAAgC,CAAC,WAAW;AAE9C,MAAM,oCAAsB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGzC,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,6LAAC,+KAAA,CAAA,SAA4B;kBAC3B,cAAA,6LAAC,+KAAA,CAAA,UAA6B;YAC5B,KAAK;YACL,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ybACA;YAED,GAAG,KAAK;;;;;;;;;;;;AAIf,oBAAoB,WAAW,GAAG,+KAAA,CAAA,UAA6B,CAAC,WAAW;AAE3E,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAKtC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,6LAAC,+KAAA,CAAA,OAA0B;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mOACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;;AAGb,iBAAiB,WAAW,GAAG,+KAAA,CAAA,OAA0B,CAAC,WAAW;AAErE,MAAM,yCAA2B,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG9C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBAC7C,6LAAC,+KAAA,CAAA,eAAkC;QACjC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGpB;;;;;;;;AAGL,yBAAyB,WAAW,GAClC,+KAAA,CAAA,eAAkC,CAAC,WAAW;AAEhD,MAAM,sCAAwB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAG3C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,yMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGrB;;;;;;;;AAGL,sBAAsB,WAAW,GAAG,+KAAA,CAAA,YAA+B,CAAC,WAAW;AAE/E,MAAM,kCAAoB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAKvC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,6LAAC,+KAAA,CAAA,QAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qCACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;;AAGb,kBAAkB,WAAW,GAAG,+KAAA,CAAA,QAA2B,CAAC,WAAW;AAEvE,MAAM,sCAAwB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAG3C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;;AAGb,sBAAsB,WAAW,GAAG,+KAAA,CAAA,YAA+B,CAAC,WAAW;AAE/E,MAAM,uBAAuB,CAAC,EAC5B,SAAS,EACT,GAAG,OACmC;IACtC,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8CAA8C;QAC3D,GAAG,KAAK;;;;;;AAGf;OAVM;AAWN,qBAAqB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 820, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/Node/my-assistant-v3/src/components/ChatGPTInterface.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { useState, FormEvent } from \"react\"\nimport { Paperclip, Mic, CornerDownLeft, MessageSquare, Plus, Settings, User, History, Search, FileText, Database, MoreVertical, Edit2, Trash2 } from \"lucide-react\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { ChatInput, ChatMessageList, Message, Mission } from \"@/components/ui/chat-interface\"\nimport KnowledgeBaseManager from \"@/components/KnowledgeBaseManager\"\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\"\nimport { cn } from \"@/lib/utils\"\n\n// Main Chat Interface Component\nconst ChatGPTInterface: React.FC = () => {\n  const [messages, setMessages] = useState<Message[]>([\n    {\n      id: 1,\n      content: \"Hello! I'm your AI assistant. How can I help you with your mission today?\",\n      sender: \"ai\",\n      timestamp: new Date(),\n    },\n  ])\n\n  const [missions, setMissions] = useState<Mission[]>([\n    {\n      id: 1,\n      title: \"Data Analysis Project\",\n      timestamp: new Date(Date.now() - 86400000),\n      status: \"completed\",\n    },\n    {\n      id: 2,\n      title: \"Market Research\",\n      timestamp: new Date(Date.now() - 172800000),\n      status: \"completed\",\n    },\n    {\n      id: 3,\n      title: \"Content Strategy\",\n      timestamp: new Date(Date.now() - 259200000),\n      status: \"in-progress\",\n    },\n  ])\n\n  const [input, setInput] = useState(\"\")\n  const [isLoading, setIsLoading] = useState(false)\n  const [sidebarOpen, setSidebarOpen] = useState(true)\n  const [currentView, setCurrentView] = useState<\"chat\" | \"knowledge\">(\"chat\")\n  const [selectedMissionId, setSelectedMissionId] = useState<number | null>(null)\n\n  const handleSubmit = (e: FormEvent) => {\n    e.preventDefault()\n    if (!input.trim()) return\n\n    const newMessage: Message = {\n      id: messages.length + 1,\n      content: input,\n      sender: \"user\",\n      timestamp: new Date(),\n    }\n\n    setMessages((prev) => [...prev, newMessage])\n    setInput(\"\")\n    setIsLoading(true)\n\n    // Simulate AI response\n    setTimeout(() => {\n      const aiResponse: Message = {\n        id: messages.length + 2,\n        content: \"I understand your request. Let me help you with that mission. I'll analyze the requirements and provide you with a comprehensive solution.\",\n        sender: \"ai\",\n        timestamp: new Date(),\n      }\n      setMessages((prev) => [...prev, aiResponse])\n      setIsLoading(false)\n    }, 1500)\n  }\n\n  const startNewMission = () => {\n    const newMission: Mission = {\n      id: missions.length + 1,\n      title: \"New Mission\",\n      timestamp: new Date(),\n      status: \"in-progress\",\n    }\n    setMissions((prev) => [newMission, ...prev])\n    setSelectedMissionId(newMission.id)\n    setCurrentView(\"chat\")\n    setMessages([\n      {\n        id: 1,\n        content: \"Hello! I'm ready to help you with your new mission. What would you like to accomplish?\",\n        sender: \"ai\",\n        timestamp: new Date(),\n      },\n    ])\n  }\n\n  const selectMission = (missionId: number) => {\n    setSelectedMissionId(missionId)\n    setCurrentView(\"chat\")\n    // Load mission-specific messages here\n    setMessages([\n      {\n        id: 1,\n        content: `Loaded mission ${missionId}. How can I help you continue with this mission?`,\n        sender: \"ai\",\n        timestamp: new Date(),\n      },\n    ])\n  }\n\n  const renameMission = (missionId: number, newTitle: string) => {\n    setMissions(prev =>\n      prev.map(mission =>\n        mission.id === missionId ? { ...mission, title: newTitle } : mission\n      )\n    )\n  }\n\n  const deleteMission = (missionId: number) => {\n    setMissions(prev => prev.filter(mission => mission.id !== missionId))\n    if (selectedMissionId === missionId) {\n      setSelectedMissionId(null)\n      setCurrentView(\"chat\")\n    }\n  }\n\n  return (\n    <div className=\"flex h-screen bg-background text-foreground\">\n      {/* Sidebar */}\n      <div className={cn(\n        \"flex flex-col bg-muted/30 border-r border-border transition-all duration-300\",\n        sidebarOpen ? \"w-80\" : \"w-0 overflow-hidden\"\n      )}>\n        <div className=\"p-4 border-b border-border\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <h2 className=\"text-lg font-semibold\">Mission Control</h2>\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              onClick={() => setSidebarOpen(!sidebarOpen)}\n            >\n              <MessageSquare className=\"h-4 w-4\" />\n            </Button>\n          </div>\n          <div className=\"space-y-2\">\n            <Button\n              onClick={startNewMission}\n              className=\"w-full gap-2\"\n              variant=\"default\"\n            >\n              <Plus className=\"h-4 w-4\" />\n              New Mission\n            </Button>\n            <Button\n              variant={currentView === \"knowledge\" ? \"default\" : \"ghost\"}\n              className=\"w-full justify-start gap-2\"\n              onClick={() => setCurrentView(\"knowledge\")}\n            >\n              <Database className=\"h-4 w-4\" />\n              Knowledge Base\n            </Button>\n          </div>\n        </div>\n\n        <div className=\"flex-1 overflow-y-auto p-4\">\n          <div className=\"mb-4\">\n            <div className=\"relative\">\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n              <input\n                type=\"text\"\n                placeholder=\"Search missions...\"\n                className=\"w-full pl-10 pr-4 py-2 bg-background border border-border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-ring\"\n              />\n            </div>\n          </div>\n\n          <div className=\"space-y-2\">\n            <h3 className=\"text-sm font-medium text-muted-foreground mb-2\">Recent Missions</h3>\n            {missions.map((mission) => (\n              <div\n                key={mission.id}\n                className={cn(\n                  \"p-3 rounded-lg cursor-pointer border transition-colors group\",\n                  selectedMissionId === mission.id\n                    ? \"bg-primary/10 border-primary/30\"\n                    : \"bg-background hover:bg-muted/50 border-border/50\"\n                )}\n                onClick={() => selectMission(mission.id)}\n              >\n                <div className=\"flex items-start justify-between\">\n                  <div className=\"flex-1 min-w-0\">\n                    <h4 className=\"text-sm font-medium truncate\">{mission.title}</h4>\n                    <p className=\"text-xs text-muted-foreground mt-1\">\n                      {mission.timestamp.toLocaleDateString()}\n                    </p>\n                  </div>\n                  <div className=\"flex items-center gap-2\">\n                    <div className={cn(\n                      \"w-2 h-2 rounded-full\",\n                      mission.status === \"completed\" && \"bg-green-500\",\n                      mission.status === \"in-progress\" && \"bg-yellow-500\",\n                      mission.status === \"failed\" && \"bg-red-500\"\n                    )} />\n                    <DropdownMenu>\n                      <DropdownMenuTrigger asChild>\n                        <Button\n                          variant=\"ghost\"\n                          size=\"icon\"\n                          className=\"h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity\"\n                          onClick={(e) => e.stopPropagation()}\n                        >\n                          <MoreVertical className=\"h-3 w-3\" />\n                        </Button>\n                      </DropdownMenuTrigger>\n                      <DropdownMenuContent align=\"end\">\n                        <DropdownMenuItem\n                          onClick={(e) => {\n                            e.stopPropagation()\n                            const newTitle = prompt(\"Enter new mission title:\", mission.title)\n                            if (newTitle && newTitle.trim()) {\n                              renameMission(mission.id, newTitle.trim())\n                            }\n                          }}\n                        >\n                          <Edit2 className=\"h-4 w-4 mr-2\" />\n                          Rename\n                        </DropdownMenuItem>\n                        <DropdownMenuItem\n                          onClick={(e) => {\n                            e.stopPropagation()\n                            if (confirm(\"Are you sure you want to delete this mission?\")) {\n                              deleteMission(mission.id)\n                            }\n                          }}\n                          className=\"text-red-600\"\n                        >\n                          <Trash2 className=\"h-4 w-4 mr-2\" />\n                          Delete\n                        </DropdownMenuItem>\n                      </DropdownMenuContent>\n                    </DropdownMenu>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        <div className=\"p-4 border-t border-border\">\n          <div className=\"flex items-center gap-3\">\n            <div className=\"w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center\">\n              <User className=\"h-4 w-4\" />\n            </div>\n            <div className=\"flex-1 min-w-0\">\n              <p className=\"text-sm font-medium\">User</p>\n              <p className=\"text-xs text-muted-foreground\">Free Plan</p>\n            </div>\n            <Button variant=\"ghost\" size=\"icon\">\n              <Settings className=\"h-4 w-4\" />\n            </Button>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Content */}\n      <div className=\"flex-1 flex flex-col\">\n        {/* Header */}\n        <div className=\"p-4 border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center gap-3\">\n              {!sidebarOpen && (\n                <Button\n                  variant=\"ghost\"\n                  size=\"icon\"\n                  onClick={() => setSidebarOpen(true)}\n                >\n                  <MessageSquare className=\"h-4 w-4\" />\n                </Button>\n              )}\n              <div>\n                <h1 className=\"text-lg font-semibold\">\n                  {currentView === \"chat\" ? \"Current Mission\" : \"Knowledge Base\"}\n                </h1>\n                <p className=\"text-sm text-muted-foreground\">\n                  {currentView === \"chat\" ? \"AI Assistant Ready\" : \"Manage your knowledge resources\"}\n                </p>\n              </div>\n            </div>\n            <div className=\"flex items-center gap-2\">\n              <Button variant=\"ghost\" size=\"icon\">\n                <History className=\"h-4 w-4\" />\n              </Button>\n            </div>\n          </div>\n        </div>\n\n        {/* Main Content Area */}\n        <div className=\"flex-1 overflow-hidden\">\n          {currentView === \"chat\" ? (\n            <ChatMessageList>\n              {messages.map((message) => (\n                <div\n                  key={message.id}\n                  className={cn(\n                    \"flex gap-3 max-w-3xl\",\n                    message.sender === \"user\" ? \"ml-auto flex-row-reverse\" : \"\"\n                  )}\n                >\n                  <div className=\"w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center shrink-0\">\n                    {message.sender === \"user\" ? (\n                      <User className=\"h-4 w-4\" />\n                    ) : (\n                      <MessageSquare className=\"h-4 w-4\" />\n                    )}\n                  </div>\n                  <div\n                    className={cn(\n                      \"rounded-lg px-4 py-3 max-w-[80%]\",\n                      message.sender === \"user\"\n                        ? \"bg-primary text-primary-foreground\"\n                        : \"bg-muted\"\n                    )}\n                  >\n                    <p className=\"text-sm\">{message.content}</p>\n                    <p className=\"text-xs opacity-70 mt-1\">\n                      {message.timestamp.toLocaleTimeString()}\n                    </p>\n                  </div>\n                </div>\n              ))}\n\n              {isLoading && (\n                <div className=\"flex gap-3 max-w-3xl\">\n                  <div className=\"w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center shrink-0\">\n                    <MessageSquare className=\"h-4 w-4\" />\n                  </div>\n                  <div className=\"rounded-lg px-4 py-3 bg-muted\">\n                    <div className=\"flex items-center gap-1\">\n                      <div className=\"w-2 h-2 bg-current rounded-full animate-pulse\" />\n                      <div className=\"w-2 h-2 bg-current rounded-full animate-pulse delay-100\" />\n                      <div className=\"w-2 h-2 bg-current rounded-full animate-pulse delay-200\" />\n                    </div>\n                  </div>\n                </div>\n              )}\n            </ChatMessageList>\n          ) : (\n            <KnowledgeBaseManager />\n          )}\n        </div>\n\n        {/* Input - Only show for chat view */}\n        {currentView === \"chat\" && (\n          <div className=\"p-4 border-t border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\n            <form\n              onSubmit={handleSubmit}\n              className=\"relative rounded-lg border bg-background focus-within:ring-1 focus-within:ring-ring p-1 max-w-4xl mx-auto\"\n            >\n              <ChatInput\n                value={input}\n                onChange={(e) => setInput(e.target.value)}\n                placeholder=\"Describe your mission or ask for help...\"\n                className=\"min-h-12 resize-none rounded-lg bg-background border-0 p-3 shadow-none focus-visible:ring-0\"\n              />\n              <div className=\"flex items-center p-3 pt-0 justify-between\">\n                <div className=\"flex\">\n                  <Button\n                    variant=\"ghost\"\n                    size=\"icon\"\n                    type=\"button\"\n                  >\n                    <Paperclip className=\"size-4\" />\n                  </Button>\n\n                  <Button\n                    variant=\"ghost\"\n                    size=\"icon\"\n                    type=\"button\"\n                  >\n                    <Mic className=\"size-4\" />\n                  </Button>\n                </div>\n                <Button\n                  type=\"submit\"\n                  size=\"sm\"\n                  className=\"ml-auto gap-1.5\"\n                  disabled={!input.trim() || isLoading}\n                >\n                  Send Mission\n                  <CornerDownLeft className=\"size-3.5\" />\n                </Button>\n              </div>\n            </form>\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n\nexport default ChatGPTInterface\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAMA;;;AAdA;;;;;;;;AAgBA,gCAAgC;AAChC,MAAM,mBAA6B;;IACjC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa;QAClD;YACE,IAAI;YACJ,SAAS;YACT,QAAQ;YACR,WAAW,IAAI;QACjB;KACD;IAED,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa;QAClD;YACE,IAAI;YACJ,OAAO;YACP,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK;YACjC,QAAQ;QACV;QACA;YACE,IAAI;YACJ,OAAO;YACP,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK;YACjC,QAAQ;QACV;QACA;YACE,IAAI;YACJ,OAAO;YACP,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK;YACjC,QAAQ;QACV;KACD;IAED,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;IACrE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAE1E,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,CAAC,MAAM,IAAI,IAAI;QAEnB,MAAM,aAAsB;YAC1B,IAAI,SAAS,MAAM,GAAG;YACtB,SAAS;YACT,QAAQ;YACR,WAAW,IAAI;QACjB;QAEA,YAAY,CAAC,OAAS;mBAAI;gBAAM;aAAW;QAC3C,SAAS;QACT,aAAa;QAEb,uBAAuB;QACvB,WAAW;YACT,MAAM,aAAsB;gBAC1B,IAAI,SAAS,MAAM,GAAG;gBACtB,SAAS;gBACT,QAAQ;gBACR,WAAW,IAAI;YACjB;YACA,YAAY,CAAC,OAAS;uBAAI;oBAAM;iBAAW;YAC3C,aAAa;QACf,GAAG;IACL;IAEA,MAAM,kBAAkB;QACtB,MAAM,aAAsB;YAC1B,IAAI,SAAS,MAAM,GAAG;YACtB,OAAO;YACP,WAAW,IAAI;YACf,QAAQ;QACV;QACA,YAAY,CAAC,OAAS;gBAAC;mBAAe;aAAK;QAC3C,qBAAqB,WAAW,EAAE;QAClC,eAAe;QACf,YAAY;YACV;gBACE,IAAI;gBACJ,SAAS;gBACT,QAAQ;gBACR,WAAW,IAAI;YACjB;SACD;IACH;IAEA,MAAM,gBAAgB,CAAC;QACrB,qBAAqB;QACrB,eAAe;QACf,sCAAsC;QACtC,YAAY;YACV;gBACE,IAAI;gBACJ,SAAS,CAAC,eAAe,EAAE,UAAU,gDAAgD,CAAC;gBACtF,QAAQ;gBACR,WAAW,IAAI;YACjB;SACD;IACH;IAEA,MAAM,gBAAgB,CAAC,WAAmB;QACxC,YAAY,CAAA,OACV,KAAK,GAAG,CAAC,CAAA,UACP,QAAQ,EAAE,KAAK,YAAY;oBAAE,GAAG,OAAO;oBAAE,OAAO;gBAAS,IAAI;IAGnE;IAEA,MAAM,gBAAgB,CAAC;QACrB,YAAY,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;QAC1D,IAAI,sBAAsB,WAAW;YACnC,qBAAqB;YACrB,eAAe;QACjB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,gFACA,cAAc,SAAS;;kCAEvB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAwB;;;;;;kDACtC,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,eAAe,CAAC;kDAE/B,cAAA,6LAAC,2NAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAG7B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS;wCACT,WAAU;wCACV,SAAQ;;0DAER,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAG9B,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS,gBAAgB,cAAc,YAAY;wCACnD,WAAU;wCACV,SAAS,IAAM,eAAe;;0DAE9B,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;;;;;;;;kCAMtC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,WAAU;;;;;;;;;;;;;;;;;0CAKhB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAiD;;;;;;oCAC9D,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC;4CAEC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gEACA,sBAAsB,QAAQ,EAAE,GAC5B,oCACA;4CAEN,SAAS,IAAM,cAAc,QAAQ,EAAE;sDAEvC,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAU;0EAAgC,QAAQ,KAAK;;;;;;0EAC3D,6LAAC;gEAAE,WAAU;0EACV,QAAQ,SAAS,CAAC,kBAAkB;;;;;;;;;;;;kEAGzC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,wBACA,QAAQ,MAAM,KAAK,eAAe,gBAClC,QAAQ,MAAM,KAAK,iBAAiB,iBACpC,QAAQ,MAAM,KAAK,YAAY;;;;;;0EAEjC,6LAAC,+IAAA,CAAA,eAAY;;kFACX,6LAAC,+IAAA,CAAA,sBAAmB;wEAAC,OAAO;kFAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;4EACL,SAAQ;4EACR,MAAK;4EACL,WAAU;4EACV,SAAS,CAAC,IAAM,EAAE,eAAe;sFAEjC,cAAA,6LAAC,6NAAA,CAAA,eAAY;gFAAC,WAAU;;;;;;;;;;;;;;;;kFAG5B,6LAAC,+IAAA,CAAA,sBAAmB;wEAAC,OAAM;;0FACzB,6LAAC,+IAAA,CAAA,mBAAgB;gFACf,SAAS,CAAC;oFACR,EAAE,eAAe;oFACjB,MAAM,WAAW,OAAO,4BAA4B,QAAQ,KAAK;oFACjE,IAAI,YAAY,SAAS,IAAI,IAAI;wFAC/B,cAAc,QAAQ,EAAE,EAAE,SAAS,IAAI;oFACzC;gFACF;;kGAEA,6LAAC,qMAAA,CAAA,QAAK;wFAAC,WAAU;;;;;;oFAAiB;;;;;;;0FAGpC,6LAAC,+IAAA,CAAA,mBAAgB;gFACf,SAAS,CAAC;oFACR,EAAE,eAAe;oFACjB,IAAI,QAAQ,kDAAkD;wFAC5D,cAAc,QAAQ,EAAE;oFAC1B;gFACF;gFACA,WAAU;;kGAEV,6LAAC,6MAAA,CAAA,SAAM;wFAAC,WAAU;;;;;;oFAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2CAxDxC,QAAQ,EAAE;;;;;;;;;;;;;;;;;kCAoEvB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAElB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAsB;;;;;;sDACnC,6LAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;8CAE/C,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;8CAC3B,cAAA,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO5B,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;wCACZ,CAAC,6BACA,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,eAAe;sDAE9B,cAAA,6LAAC,2NAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;;;;;;sDAG7B,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DACX,gBAAgB,SAAS,oBAAoB;;;;;;8DAEhD,6LAAC;oDAAE,WAAU;8DACV,gBAAgB,SAAS,uBAAuB;;;;;;;;;;;;;;;;;;8CAIvD,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,MAAK;kDAC3B,cAAA,6LAAC,2MAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO3B,6LAAC;wBAAI,WAAU;kCACZ,gBAAgB,uBACf,6LAAC,gJAAA,CAAA,kBAAe;;gCACb,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC;wCAEC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wBACA,QAAQ,MAAM,KAAK,SAAS,6BAA6B;;0DAG3D,6LAAC;gDAAI,WAAU;0DACZ,QAAQ,MAAM,KAAK,uBAClB,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;yEAEhB,6LAAC,2NAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;;;;;;0DAG7B,6LAAC;gDACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oCACA,QAAQ,MAAM,KAAK,SACf,uCACA;;kEAGN,6LAAC;wDAAE,WAAU;kEAAW,QAAQ,OAAO;;;;;;kEACvC,6LAAC;wDAAE,WAAU;kEACV,QAAQ,SAAS,CAAC,kBAAkB;;;;;;;;;;;;;uCAvBpC,QAAQ,EAAE;;;;;gCA6BlB,2BACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,2NAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;;;;;;sDAE3B,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;iDAOzB,6LAAC,6IAAA,CAAA,UAAoB;;;;;;;;;;oBAKxB,gBAAgB,wBACf,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,UAAU;4BACV,WAAU;;8CAEV,6LAAC,gJAAA,CAAA,YAAS;oCACR,OAAO;oCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;oCACxC,aAAY;oCACZ,WAAU;;;;;;8CAEZ,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,MAAK;8DAEL,cAAA,6LAAC,+MAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;;;;;;8DAGvB,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,MAAK;8DAEL,cAAA,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAGnB,6LAAC,qIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,MAAK;4CACL,WAAU;4CACV,UAAU,CAAC,MAAM,IAAI,MAAM;;gDAC5B;8DAEC,6LAAC,iOAAA,CAAA,iBAAc;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS5C;GAnYM;KAAA;uCAqYS", "debugId": null}}]}