{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/Node/my-assistant-v3/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/Node/my-assistant-v3/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/Node/my-assistant-v3/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS", "debugId": null}}, {"offset": {"line": 120, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/Node/my-assistant-v3/src/components/ui/chat-interface.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { useState, useCallback, useEffect, useRef, FormEvent } from \"react\"\nimport { ArrowDown, Paperclip, Mic, CornerDownLeft, MessageSquare, Plus, Settings, User, History, Search } from \"lucide-react\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Textarea } from \"@/components/ui/textarea\"\nimport { cn } from \"@/lib/utils\"\n\n// Auto-scroll hook\ninterface ScrollState {\n  isAtBottom: boolean\n  autoScrollEnabled: boolean\n}\n\ninterface UseAutoScrollOptions {\n  offset?: number\n  smooth?: boolean\n  content?: React.ReactNode\n}\n\nfunction useAutoScroll(options: UseAutoScrollOptions = {}) {\n  const { offset = 20, smooth = false, content } = options\n  const scrollRef = useRef<HTMLDivElement>(null)\n  const lastContentHeight = useRef(0)\n  const userHasScrolled = useRef(false)\n\n  const [scrollState, setScrollState] = useState<ScrollState>({\n    isAtBottom: true,\n    autoScrollEnabled: true,\n  })\n\n  const checkIsAtBottom = useCallback(\n    (element: HTMLElement) => {\n      const { scrollTop, scrollHeight, clientHeight } = element\n      const distanceToBottom = Math.abs(\n        scrollHeight - scrollTop - clientHeight\n      )\n      return distanceToBottom <= offset\n    },\n    [offset]\n  )\n\n  const scrollToBottom = useCallback(\n    (instant?: boolean) => {\n      if (!scrollRef.current) return\n\n      const targetScrollTop =\n        scrollRef.current.scrollHeight - scrollRef.current.clientHeight\n\n      if (instant) {\n        scrollRef.current.scrollTop = targetScrollTop\n      } else {\n        scrollRef.current.scrollTo({\n          top: targetScrollTop,\n          behavior: smooth ? \"smooth\" : \"auto\",\n        })\n      }\n\n      setScrollState({\n        isAtBottom: true,\n        autoScrollEnabled: true,\n      })\n      userHasScrolled.current = false\n    },\n    [smooth]\n  )\n\n  const handleScroll = useCallback(() => {\n    if (!scrollRef.current) return\n\n    const atBottom = checkIsAtBottom(scrollRef.current)\n\n    setScrollState((prev) => ({\n      isAtBottom: atBottom,\n      autoScrollEnabled: atBottom ? true : prev.autoScrollEnabled,\n    }))\n  }, [checkIsAtBottom])\n\n  useEffect(() => {\n    const element = scrollRef.current\n    if (!element) return\n\n    element.addEventListener(\"scroll\", handleScroll, { passive: true })\n    return () => element.removeEventListener(\"scroll\", handleScroll)\n  }, [handleScroll])\n\n  useEffect(() => {\n    const scrollElement = scrollRef.current\n    if (!scrollElement) return\n\n    const currentHeight = scrollElement.scrollHeight\n    const hasNewContent = currentHeight !== lastContentHeight.current\n\n    if (hasNewContent) {\n      if (scrollState.autoScrollEnabled) {\n        requestAnimationFrame(() => {\n          scrollToBottom(lastContentHeight.current === 0)\n        })\n      }\n      lastContentHeight.current = currentHeight\n    }\n  }, [content, scrollState.autoScrollEnabled, scrollToBottom])\n\n  const disableAutoScroll = useCallback(() => {\n    const atBottom = scrollRef.current\n      ? checkIsAtBottom(scrollRef.current)\n      : false\n\n    if (!atBottom) {\n      userHasScrolled.current = true\n      setScrollState((prev) => ({\n        ...prev,\n        autoScrollEnabled: false,\n      }))\n    }\n  }, [checkIsAtBottom])\n\n  return {\n    scrollRef,\n    isAtBottom: scrollState.isAtBottom,\n    autoScrollEnabled: scrollState.autoScrollEnabled,\n    scrollToBottom: () => scrollToBottom(false),\n    disableAutoScroll,\n  }\n}\n\n// Chat Input Component\ninterface ChatInputProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}\n\nconst ChatInput = React.forwardRef<HTMLTextAreaElement, ChatInputProps>(\n  ({ className, ...props }, ref) => (\n    <Textarea\n      autoComplete=\"off\"\n      ref={ref}\n      name=\"message\"\n      className={cn(\n        \"max-h-12 px-4 py-3 bg-background text-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 w-full rounded-md flex items-center h-16 resize-none\",\n        className,\n      )}\n      {...props}\n    />\n  ),\n)\nChatInput.displayName = \"ChatInput\"\n\n// Chat Message List Component\ninterface ChatMessageListProps extends React.HTMLAttributes<HTMLDivElement> {\n  smooth?: boolean\n}\n\nconst ChatMessageList = React.forwardRef<HTMLDivElement, ChatMessageListProps>(\n  ({ className, children, smooth = false, ...props }, _ref) => {\n    const {\n      scrollRef,\n      isAtBottom,\n      autoScrollEnabled,\n      scrollToBottom,\n      disableAutoScroll,\n    } = useAutoScroll({\n      smooth,\n      content: children,\n    })\n\n    return (\n      <div className=\"relative w-full h-full\">\n        <div\n          className={`flex flex-col w-full h-full p-4 overflow-y-auto ${className}`}\n          ref={scrollRef}\n          onWheel={disableAutoScroll}\n          onTouchMove={disableAutoScroll}\n          {...props}\n        >\n          <div className=\"flex flex-col gap-6\">{children}</div>\n        </div>\n\n        {!isAtBottom && (\n          <Button\n            onClick={() => {\n              scrollToBottom()\n            }}\n            size=\"icon\"\n            variant=\"outline\"\n            className=\"absolute bottom-2 left-1/2 transform -translate-x-1/2 inline-flex rounded-full shadow-md\"\n            aria-label=\"Scroll to bottom\"\n          >\n            <ArrowDown className=\"h-4 w-4\" />\n          </Button>\n        )}\n      </div>\n    )\n  }\n)\n\nChatMessageList.displayName = \"ChatMessageList\"\n\n// Message Interface\ninterface Message {\n  id: number\n  content: string\n  sender: \"user\" | \"ai\"\n  timestamp: Date\n}\n\ninterface Mission {\n  id: number\n  title: string\n  timestamp: Date\n  status: \"completed\" | \"in-progress\" | \"failed\"\n}\n\nexport { ChatInput, ChatMessageList, useAutoScroll, type Message, type Mission }\n"], "names": [], "mappings": ";;;;;;AAEA;AAEA;AACA;AACA;AACA;;;AAPA;;;;;;;AAqBA,SAAS,cAAc,UAAgC,CAAC,CAAC;;IACvD,MAAM,EAAE,SAAS,EAAE,EAAE,SAAS,KAAK,EAAE,OAAO,EAAE,GAAG;IACjD,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IACzC,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACjC,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAE/B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;QAC1D,YAAY;QACZ,mBAAmB;IACrB;IAEA,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAChC,CAAC;YACC,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,YAAY,EAAE,GAAG;YAClD,MAAM,mBAAmB,KAAK,GAAG,CAC/B,eAAe,YAAY;YAE7B,OAAO,oBAAoB;QAC7B;qDACA;QAAC;KAAO;IAGV,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAC/B,CAAC;YACC,IAAI,CAAC,UAAU,OAAO,EAAE;YAExB,MAAM,kBACJ,UAAU,OAAO,CAAC,YAAY,GAAG,UAAU,OAAO,CAAC,YAAY;YAEjE,IAAI,SAAS;gBACX,UAAU,OAAO,CAAC,SAAS,GAAG;YAChC,OAAO;gBACL,UAAU,OAAO,CAAC,QAAQ,CAAC;oBACzB,KAAK;oBACL,UAAU,SAAS,WAAW;gBAChC;YACF;YAEA,eAAe;gBACb,YAAY;gBACZ,mBAAmB;YACrB;YACA,gBAAgB,OAAO,GAAG;QAC5B;oDACA;QAAC;KAAO;IAGV,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE;YAC/B,IAAI,CAAC,UAAU,OAAO,EAAE;YAExB,MAAM,WAAW,gBAAgB,UAAU,OAAO;YAElD;2DAAe,CAAC,OAAS,CAAC;wBACxB,YAAY;wBACZ,mBAAmB,WAAW,OAAO,KAAK,iBAAiB;oBAC7D,CAAC;;QACH;kDAAG;QAAC;KAAgB;IAEpB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM,UAAU,UAAU,OAAO;YACjC,IAAI,CAAC,SAAS;YAEd,QAAQ,gBAAgB,CAAC,UAAU,cAAc;gBAAE,SAAS;YAAK;YACjE;2CAAO,IAAM,QAAQ,mBAAmB,CAAC,UAAU;;QACrD;kCAAG;QAAC;KAAa;IAEjB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM,gBAAgB,UAAU,OAAO;YACvC,IAAI,CAAC,eAAe;YAEpB,MAAM,gBAAgB,cAAc,YAAY;YAChD,MAAM,gBAAgB,kBAAkB,kBAAkB,OAAO;YAEjE,IAAI,eAAe;gBACjB,IAAI,YAAY,iBAAiB,EAAE;oBACjC;mDAAsB;4BACpB,eAAe,kBAAkB,OAAO,KAAK;wBAC/C;;gBACF;gBACA,kBAAkB,OAAO,GAAG;YAC9B;QACF;kCAAG;QAAC;QAAS,YAAY,iBAAiB;QAAE;KAAe;IAE3D,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE;YACpC,MAAM,WAAW,UAAU,OAAO,GAC9B,gBAAgB,UAAU,OAAO,IACjC;YAEJ,IAAI,CAAC,UAAU;gBACb,gBAAgB,OAAO,GAAG;gBAC1B;oEAAe,CAAC,OAAS,CAAC;4BACxB,GAAG,IAAI;4BACP,mBAAmB;wBACrB,CAAC;;YACH;QACF;uDAAG;QAAC;KAAgB;IAEpB,OAAO;QACL;QACA,YAAY,YAAY,UAAU;QAClC,mBAAmB,YAAY,iBAAiB;QAChD,gBAAgB,IAAM,eAAe;QACrC;IACF;AACF;GAxGS;AA6GT,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,6LAAC,uIAAA,CAAA,WAAQ;QACP,cAAa;QACb,KAAK;QACL,MAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sOACA;QAED,GAAG,KAAK;;;;;;;AAIf,UAAU,WAAW,GAAG;AAOxB,MAAM,gCAAkB,IAAA,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,YACrC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,KAAK,EAAE,GAAG,OAAO,EAAE;;IAClD,MAAM,EACJ,SAAS,EACT,UAAU,EACV,iBAAiB,EACjB,cAAc,EACd,iBAAiB,EAClB,GAAG,cAAc;QAChB;QACA,SAAS;IACX;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,WAAW,CAAC,gDAAgD,EAAE,WAAW;gBACzE,KAAK;gBACL,SAAS;gBACT,aAAa;gBACZ,GAAG,KAAK;0BAET,cAAA,6LAAC;oBAAI,WAAU;8BAAuB;;;;;;;;;;;YAGvC,CAAC,4BACA,6LAAC,qIAAA,CAAA,SAAM;gBACL,SAAS;oBACP;gBACF;gBACA,MAAK;gBACL,SAAQ;gBACR,WAAU;gBACV,cAAW;0BAEX,cAAA,6LAAC,mNAAA,CAAA,YAAS;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAK/B;;QAhCM;;;;QAAA;;;;AAmCR,gBAAgB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 348, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/Node/my-assistant-v3/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 380, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/Node/my-assistant-v3/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 495, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/Node/my-assistant-v3/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 547, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/Node/my-assistant-v3/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Separator({\n  className,\n  orientation = \"horizontal\",\n  decorative = true,\n  ...props\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\n  return (\n    <SeparatorPrimitive.Root\n      data-slot=\"separator\"\n      decorative={decorative}\n      orientation={orientation}\n      className={cn(\n        \"bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Separator }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,YAAY,EAC1B,aAAa,IAAI,EACjB,GAAG,OACkD;IACrD,qBACE,6LAAC,wKAAA,CAAA,OAAuB;QACtB,aAAU;QACV,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;;;;;AAGf;KAlBS", "debugId": null}}, {"offset": {"line": 583, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/Node/my-assistant-v3/src/components/KnowledgeBaseManager.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { useState, useEffect } from \"react\"\nimport { \n  FileText, \n  Plus, \n  Search, \n  ExternalLink, \n  RefreshCw, \n  Trash2, \n  Eye, \n  Calendar,\n  Hash,\n  CheckCircle,\n  AlertCircle,\n  Clock,\n  Download,\n  Upload\n} from \"lucide-react\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Input } from \"@/components/ui/input\"\nimport { Textarea } from \"@/components/ui/textarea\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Separator } from \"@/components/ui/separator\"\nimport { Progress } from \"@/components/ui/progress\"\nimport { cn } from \"@/lib/utils\"\n\ninterface KnowledgeItem {\n  id: string\n  title: string\n  url: string\n  lastIndexed: Date\n  status: \"indexed\" | \"failed\" | \"pending\"\n  wordCount: number\n  tags: string[]\n  content?: string\n}\n\ninterface KnowledgeBaseManagerProps {\n  className?: string\n}\n\nconst KnowledgeBaseManager: React.FC<KnowledgeBaseManagerProps> = ({ className }) => {\n  const [activeTab, setActiveTab] = useState<\"add\" | \"manage\" | \"search\">(\"add\")\n  const [knowledgeItems, setKnowledgeItems] = useState<KnowledgeItem[]>([\n    {\n      id: \"1\",\n      title: \"API Documentation - Authentication\",\n      url: \"https://confluence.company.com/api/auth\",\n      lastIndexed: new Date(Date.now() - 86400000),\n      status: \"indexed\",\n      wordCount: 1250,\n      tags: [\"API\", \"Authentication\", \"Security\"]\n    },\n    {\n      id: \"2\", \n      title: \"Service Deployment Guide\",\n      url: \"https://confluence.company.com/deployment/guide\",\n      lastIndexed: new Date(Date.now() - *********),\n      status: \"indexed\",\n      wordCount: 2100,\n      tags: [\"Deployment\", \"DevOps\", \"Guide\"]\n    },\n    {\n      id: \"3\",\n      title: \"Database Schema Documentation\",\n      url: \"https://confluence.company.com/db/schema\",\n      lastIndexed: new Date(Date.now() - *********),\n      status: \"failed\",\n      wordCount: 0,\n      tags: [\"Database\", \"Schema\"]\n    }\n  ])\n\n  const [newUrl, setNewUrl] = useState(\"\")\n  const [bulkUrls, setBulkUrls] = useState(\"\")\n  const [searchQuery, setSearchQuery] = useState(\"\")\n  const [selectedItems, setSelectedItems] = useState<string[]>([])\n  const [isIndexing, setIsIndexing] = useState(false)\n\n  const handleAddUrl = async () => {\n    if (!newUrl.trim()) return\n\n    const newItem: KnowledgeItem = {\n      id: Date.now().toString(),\n      title: \"Loading...\",\n      url: newUrl,\n      lastIndexed: new Date(),\n      status: \"pending\",\n      wordCount: 0,\n      tags: []\n    }\n\n    setKnowledgeItems(prev => [newItem, ...prev])\n    setNewUrl(\"\")\n    \n    // Simulate indexing process\n    setTimeout(() => {\n      setKnowledgeItems(prev => \n        prev.map(item => \n          item.id === newItem.id \n            ? { ...item, title: \"New Knowledge Item\", status: \"indexed\" as const, wordCount: 850, tags: [\"New\"] }\n            : item\n        )\n      )\n    }, 2000)\n  }\n\n  const handleBulkImport = async () => {\n    const urls = bulkUrls.split('\\n').filter(url => url.trim())\n    if (urls.length === 0) return\n\n    setIsIndexing(true)\n    \n    urls.forEach((url, index) => {\n      const newItem: KnowledgeItem = {\n        id: `bulk-${Date.now()}-${index}`,\n        title: \"Loading...\",\n        url: url.trim(),\n        lastIndexed: new Date(),\n        status: \"pending\",\n        wordCount: 0,\n        tags: []\n      }\n      \n      setKnowledgeItems(prev => [newItem, ...prev])\n    })\n\n    setBulkUrls(\"\")\n    setIsIndexing(false)\n  }\n\n  const handleReindex = (id: string) => {\n    setKnowledgeItems(prev =>\n      prev.map(item =>\n        item.id === id ? { ...item, status: \"pending\" as const, lastIndexed: new Date() } : item\n      )\n    )\n\n    // Simulate reindexing\n    setTimeout(() => {\n      setKnowledgeItems(prev =>\n        prev.map(item =>\n          item.id === id ? { ...item, status: \"indexed\" as const, wordCount: Math.floor(Math.random() * 2000) + 500 } : item\n        )\n      )\n    }, 1500)\n  }\n\n  const handleDelete = (id: string) => {\n    setKnowledgeItems(prev => prev.filter(item => item.id !== id))\n    setSelectedItems(prev => prev.filter(itemId => itemId !== id))\n  }\n\n  const handleBulkDelete = () => {\n    setKnowledgeItems(prev => prev.filter(item => !selectedItems.includes(item.id)))\n    setSelectedItems([])\n  }\n\n  const toggleSelection = (id: string) => {\n    setSelectedItems(prev =>\n      prev.includes(id) ? prev.filter(itemId => itemId !== id) : [...prev, id]\n    )\n  }\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case \"indexed\": return <CheckCircle className=\"h-4 w-4 text-green-500\" />\n      case \"failed\": return <AlertCircle className=\"h-4 w-4 text-red-500\" />\n      case \"pending\": return <Clock className=\"h-4 w-4 text-yellow-500\" />\n      default: return <AlertCircle className=\"h-4 w-4 text-gray-500\" />\n    }\n  }\n\n  const getStatusBadge = (status: string) => {\n    switch (status) {\n      case \"indexed\": return <Badge variant=\"outline\" className=\"bg-green-50 text-green-700 border-green-200\">Indexed</Badge>\n      case \"failed\": return <Badge variant=\"destructive\">Failed</Badge>\n      case \"pending\": return <Badge variant=\"outline\" className=\"bg-yellow-50 text-yellow-700 border-yellow-200\">Pending</Badge>\n      default: return <Badge variant=\"outline\">Unknown</Badge>\n    }\n  }\n\n  const filteredItems = knowledgeItems.filter(item =>\n    item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||\n    item.url.toLowerCase().includes(searchQuery.toLowerCase()) ||\n    item.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))\n  )\n\n  return (\n    <div className={cn(\"w-full h-full\", className)}>\n      <div className=\"flex h-full\">\n        {/* Sidebar Navigation */}\n        <div className=\"w-64 border-r border-border bg-muted/30 p-4\">\n          <div className=\"space-y-2\">\n            <h2 className=\"text-lg font-semibold mb-4 flex items-center gap-2\">\n              <FileText className=\"h-5 w-5\" />\n              Knowledge Base\n            </h2>\n            \n            <Button\n              variant={activeTab === \"add\" ? \"default\" : \"ghost\"}\n              className=\"w-full justify-start\"\n              onClick={() => setActiveTab(\"add\")}\n            >\n              <Plus className=\"h-4 w-4 mr-2\" />\n              Add Content\n            </Button>\n            \n            <Button\n              variant={activeTab === \"manage\" ? \"default\" : \"ghost\"}\n              className=\"w-full justify-start\"\n              onClick={() => setActiveTab(\"manage\")}\n            >\n              <FileText className=\"h-4 w-4 mr-2\" />\n              Manage Content\n            </Button>\n            \n            <Button\n              variant={activeTab === \"search\" ? \"default\" : \"ghost\"}\n              className=\"w-full justify-start\"\n              onClick={() => setActiveTab(\"search\")}\n            >\n              <Search className=\"h-4 w-4 mr-2\" />\n              Search Knowledge\n            </Button>\n          </div>\n\n          <Separator className=\"my-6\" />\n\n          <div className=\"space-y-3\">\n            <h3 className=\"text-sm font-medium text-muted-foreground\">Statistics</h3>\n            <div className=\"space-y-2 text-sm\">\n              <div className=\"flex justify-between\">\n                <span>Total Items:</span>\n                <span className=\"font-medium\">{knowledgeItems.length}</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span>Indexed:</span>\n                <span className=\"font-medium text-green-600\">\n                  {knowledgeItems.filter(item => item.status === \"indexed\").length}\n                </span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span>Failed:</span>\n                <span className=\"font-medium text-red-600\">\n                  {knowledgeItems.filter(item => item.status === \"failed\").length}\n                </span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span>Pending:</span>\n                <span className=\"font-medium text-yellow-600\">\n                  {knowledgeItems.filter(item => item.status === \"pending\").length}\n                </span>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Main Content */}\n        <div className=\"flex-1 p-6 overflow-y-auto\">\n          {activeTab === \"add\" && (\n            <div className=\"space-y-6\">\n              <div>\n                <h3 className=\"text-xl font-semibold mb-2\">Add New Content</h3>\n                <p className=\"text-muted-foreground\">Index new Confluence pages or documentation</p>\n              </div>\n\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"text-lg\">Single URL</CardTitle>\n                  <CardDescription>Add a single Confluence page or document</CardDescription>\n                </CardHeader>\n                <CardContent className=\"space-y-4\">\n                  <div className=\"flex gap-2\">\n                    <Input\n                      placeholder=\"https://confluence.company.com/page-url\"\n                      value={newUrl}\n                      onChange={(e) => setNewUrl(e.target.value)}\n                      className=\"flex-1\"\n                    />\n                    <Button onClick={handleAddUrl} disabled={!newUrl.trim()}>\n                      <Plus className=\"h-4 w-4 mr-2\" />\n                      Add URL\n                    </Button>\n                  </div>\n                </CardContent>\n              </Card>\n\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"text-lg\">Bulk Import</CardTitle>\n                  <CardDescription>Import multiple URLs at once (one per line)</CardDescription>\n                </CardHeader>\n                <CardContent className=\"space-y-4\">\n                  <Textarea\n                    placeholder=\"https://confluence.company.com/page1&#10;https://confluence.company.com/page2&#10;https://confluence.company.com/page3\"\n                    value={bulkUrls}\n                    onChange={(e) => setBulkUrls(e.target.value)}\n                    rows={6}\n                  />\n                  <div className=\"flex justify-between items-center\">\n                    <span className=\"text-sm text-muted-foreground\">\n                      {bulkUrls.split('\\n').filter(url => url.trim()).length} URLs ready to import\n                    </span>\n                    <Button onClick={handleBulkImport} disabled={!bulkUrls.trim() || isIndexing}>\n                      {isIndexing ? (\n                        <>\n                          <RefreshCw className=\"h-4 w-4 mr-2 animate-spin\" />\n                          Indexing...\n                        </>\n                      ) : (\n                        <>\n                          <Upload className=\"h-4 w-4 mr-2\" />\n                          Bulk Import\n                        </>\n                      )}\n                    </Button>\n                  </div>\n                </CardContent>\n              </Card>\n            </div>\n          )}\n\n          {activeTab === \"manage\" && (\n            <div className=\"space-y-6\">\n              <div className=\"flex justify-between items-center\">\n                <div>\n                  <h3 className=\"text-xl font-semibold mb-2\">Manage Content</h3>\n                  <p className=\"text-muted-foreground\">View and manage your indexed knowledge base</p>\n                </div>\n                {selectedItems.length > 0 && (\n                  <div className=\"flex gap-2\">\n                    <Button variant=\"outline\" size=\"sm\">\n                      <RefreshCw className=\"h-4 w-4 mr-2\" />\n                      Reindex Selected ({selectedItems.length})\n                    </Button>\n                    <Button variant=\"destructive\" size=\"sm\" onClick={handleBulkDelete}>\n                      <Trash2 className=\"h-4 w-4 mr-2\" />\n                      Delete Selected ({selectedItems.length})\n                    </Button>\n                  </div>\n                )}\n              </div>\n\n              <div className=\"flex gap-2 mb-4\">\n                <div className=\"relative flex-1\">\n                  <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n                  <Input\n                    placeholder=\"Search by title, URL, or tags...\"\n                    value={searchQuery}\n                    onChange={(e) => setSearchQuery(e.target.value)}\n                    className=\"pl-10\"\n                  />\n                </div>\n                <Button variant=\"outline\">\n                  <Download className=\"h-4 w-4 mr-2\" />\n                  Export\n                </Button>\n              </div>\n\n              <div className=\"space-y-3\">\n                {filteredItems.map((item) => (\n                  <Card key={item.id} className=\"hover:shadow-md transition-shadow\">\n                    <CardContent className=\"p-4\">\n                      <div className=\"flex items-start gap-4\">\n                        <input\n                          type=\"checkbox\"\n                          checked={selectedItems.includes(item.id)}\n                          onChange={() => toggleSelection(item.id)}\n                          className=\"mt-1\"\n                        />\n                        \n                        <div className=\"flex-1 space-y-2\">\n                          <div className=\"flex items-start justify-between\">\n                            <div className=\"flex-1\">\n                              <div className=\"flex items-center gap-2 mb-1\">\n                                {getStatusIcon(item.status)}\n                                <h4 className=\"font-medium\">{item.title}</h4>\n                                {getStatusBadge(item.status)}\n                              </div>\n                              <a\n                                href={item.url}\n                                target=\"_blank\"\n                                rel=\"noopener noreferrer\"\n                                className=\"text-sm text-blue-600 hover:underline flex items-center gap-1\"\n                              >\n                                {item.url}\n                                <ExternalLink className=\"h-3 w-3\" />\n                              </a>\n                            </div>\n                            \n                            <div className=\"flex gap-1\">\n                              <Button variant=\"ghost\" size=\"sm\">\n                                <Eye className=\"h-4 w-4\" />\n                              </Button>\n                              <Button variant=\"ghost\" size=\"sm\" onClick={() => handleReindex(item.id)}>\n                                <RefreshCw className=\"h-4 w-4\" />\n                              </Button>\n                              <Button variant=\"ghost\" size=\"sm\" onClick={() => handleDelete(item.id)}>\n                                <Trash2 className=\"h-4 w-4\" />\n                              </Button>\n                            </div>\n                          </div>\n\n                          <div className=\"flex items-center gap-4 text-xs text-muted-foreground\">\n                            <div className=\"flex items-center gap-1\">\n                              <Calendar className=\"h-3 w-3\" />\n                              {item.lastIndexed.toLocaleDateString()}\n                            </div>\n                            <div className=\"flex items-center gap-1\">\n                              <Hash className=\"h-3 w-3\" />\n                              {item.wordCount.toLocaleString()} words\n                            </div>\n                          </div>\n\n                          {item.tags.length > 0 && (\n                            <div className=\"flex gap-1 flex-wrap\">\n                              {item.tags.map((tag) => (\n                                <Badge key={tag} variant=\"secondary\" className=\"text-xs\">\n                                  {tag}\n                                </Badge>\n                              ))}\n                            </div>\n                          )}\n                        </div>\n                      </div>\n                    </CardContent>\n                  </Card>\n                ))}\n              </div>\n            </div>\n          )}\n\n          {activeTab === \"search\" && (\n            <div className=\"space-y-6\">\n              <div>\n                <h3 className=\"text-xl font-semibold mb-2\">Search Knowledge Base</h3>\n                <p className=\"text-muted-foreground\">Find relevant information from your indexed content</p>\n              </div>\n\n              <Card>\n                <CardContent className=\"p-6\">\n                  <div className=\"space-y-4\">\n                    <div className=\"relative\">\n                      <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n                      <Input\n                        placeholder=\"Search for information, concepts, or specific topics...\"\n                        className=\"pl-10 text-base h-12\"\n                      />\n                    </div>\n                    \n                    <div className=\"flex gap-2 flex-wrap\">\n                      <Button variant=\"outline\" size=\"sm\">API</Button>\n                      <Button variant=\"outline\" size=\"sm\">Authentication</Button>\n                      <Button variant=\"outline\" size=\"sm\">Deployment</Button>\n                      <Button variant=\"outline\" size=\"sm\">Database</Button>\n                      <Button variant=\"outline\" size=\"sm\">Security</Button>\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n\n              <div className=\"space-y-4\">\n                <h4 className=\"font-medium\">Search Results</h4>\n                <div className=\"text-center py-12 text-muted-foreground\">\n                  <Search className=\"h-12 w-12 mx-auto mb-4 opacity-50\" />\n                  <p>Enter a search query to find relevant information</p>\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  )\n}\n\nexport default KnowledgeBaseManager\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA;AACA;AACA;AACA;AACA;AACA;AAEA;;;AA3BA;;;;;;;;;;AA4CA,MAAM,uBAA4D,CAAC,EAAE,SAAS,EAAE;;IAC9E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA+B;IACxE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;QACpE;YACE,IAAI;YACJ,OAAO;YACP,KAAK;YACL,aAAa,IAAI,KAAK,KAAK,GAAG,KAAK;YACnC,QAAQ;YACR,WAAW;YACX,MAAM;gBAAC;gBAAO;gBAAkB;aAAW;QAC7C;QACA;YACE,IAAI;YACJ,OAAO;YACP,KAAK;YACL,aAAa,IAAI,KAAK,KAAK,GAAG,KAAK;YACnC,QAAQ;YACR,WAAW;YACX,MAAM;gBAAC;gBAAc;gBAAU;aAAQ;QACzC;QACA;YACE,IAAI;YACJ,OAAO;YACP,KAAK;YACL,aAAa,IAAI,KAAK,KAAK,GAAG,KAAK;YACnC,QAAQ;YACR,WAAW;YACX,MAAM;gBAAC;gBAAY;aAAS;QAC9B;KACD;IAED,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC/D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,eAAe;QACnB,IAAI,CAAC,OAAO,IAAI,IAAI;QAEpB,MAAM,UAAyB;YAC7B,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,OAAO;YACP,KAAK;YACL,aAAa,IAAI;YACjB,QAAQ;YACR,WAAW;YACX,MAAM,EAAE;QACV;QAEA,kBAAkB,CAAA,OAAQ;gBAAC;mBAAY;aAAK;QAC5C,UAAU;QAEV,4BAA4B;QAC5B,WAAW;YACT,kBAAkB,CAAA,OAChB,KAAK,GAAG,CAAC,CAAA,OACP,KAAK,EAAE,KAAK,QAAQ,EAAE,GAClB;wBAAE,GAAG,IAAI;wBAAE,OAAO;wBAAsB,QAAQ;wBAAoB,WAAW;wBAAK,MAAM;4BAAC;yBAAM;oBAAC,IAClG;QAGV,GAAG;IACL;IAEA,MAAM,mBAAmB;QACvB,MAAM,OAAO,SAAS,KAAK,CAAC,MAAM,MAAM,CAAC,CAAA,MAAO,IAAI,IAAI;QACxD,IAAI,KAAK,MAAM,KAAK,GAAG;QAEvB,cAAc;QAEd,KAAK,OAAO,CAAC,CAAC,KAAK;YACjB,MAAM,UAAyB;gBAC7B,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,OAAO;gBACjC,OAAO;gBACP,KAAK,IAAI,IAAI;gBACb,aAAa,IAAI;gBACjB,QAAQ;gBACR,WAAW;gBACX,MAAM,EAAE;YACV;YAEA,kBAAkB,CAAA,OAAQ;oBAAC;uBAAY;iBAAK;QAC9C;QAEA,YAAY;QACZ,cAAc;IAChB;IAEA,MAAM,gBAAgB,CAAC;QACrB,kBAAkB,CAAA,OAChB,KAAK,GAAG,CAAC,CAAA,OACP,KAAK,EAAE,KAAK,KAAK;oBAAE,GAAG,IAAI;oBAAE,QAAQ;oBAAoB,aAAa,IAAI;gBAAO,IAAI;QAIxF,sBAAsB;QACtB,WAAW;YACT,kBAAkB,CAAA,OAChB,KAAK,GAAG,CAAC,CAAA,OACP,KAAK,EAAE,KAAK,KAAK;wBAAE,GAAG,IAAI;wBAAE,QAAQ;wBAAoB,WAAW,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,QAAQ;oBAAI,IAAI;QAGpH,GAAG;IACL;IAEA,MAAM,eAAe,CAAC;QACpB,kBAAkB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QAC1D,iBAAiB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,SAAU,WAAW;IAC5D;IAEA,MAAM,mBAAmB;QACvB,kBAAkB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,OAAQ,CAAC,cAAc,QAAQ,CAAC,KAAK,EAAE;QAC7E,iBAAiB,EAAE;IACrB;IAEA,MAAM,kBAAkB,CAAC;QACvB,iBAAiB,CAAA,OACf,KAAK,QAAQ,CAAC,MAAM,KAAK,MAAM,CAAC,CAAA,SAAU,WAAW,MAAM;mBAAI;gBAAM;aAAG;IAE5E;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAW,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAC9C,KAAK;gBAAU,qBAAO,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAC7C,KAAK;gBAAW,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YACxC;gBAAS,qBAAO,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;QACzC;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAW,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,SAAQ;oBAAU,WAAU;8BAA8C;;;;;;YACxG,KAAK;gBAAU,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,SAAQ;8BAAc;;;;;;YACnD,KAAK;gBAAW,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,SAAQ;oBAAU,WAAU;8BAAiD;;;;;;YAC3G;gBAAS,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,SAAQ;8BAAU;;;;;;QAC3C;IACF;IAEA,MAAM,gBAAgB,eAAe,MAAM,CAAC,CAAA,OAC1C,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OACzD,KAAK,GAAG,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OACvD,KAAK,IAAI,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;IAG1E,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;kBAClC,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC,iNAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAIlC,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAS,cAAc,QAAQ,YAAY;oCAC3C,WAAU;oCACV,SAAS,IAAM,aAAa;;sDAE5B,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAInC,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAS,cAAc,WAAW,YAAY;oCAC9C,WAAU;oCACV,SAAS,IAAM,aAAa;;sDAE5B,6LAAC,iNAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAIvC,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAS,cAAc,WAAW,YAAY;oCAC9C,WAAU;oCACV,SAAS,IAAM,aAAa;;sDAE5B,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;sCAKvC,6LAAC,wIAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;sCAErB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA4C;;;;;;8CAC1D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAK;;;;;;8DACN,6LAAC;oDAAK,WAAU;8DAAe,eAAe,MAAM;;;;;;;;;;;;sDAEtD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAK;;;;;;8DACN,6LAAC;oDAAK,WAAU;8DACb,eAAe,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,WAAW,MAAM;;;;;;;;;;;;sDAGpE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAK;;;;;;8DACN,6LAAC;oDAAK,WAAU;8DACb,eAAe,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,UAAU,MAAM;;;;;;;;;;;;sDAGnE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAK;;;;;;8DACN,6LAAC;oDAAK,WAAU;8DACb,eAAe,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,WAAW,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQ1E,6LAAC;oBAAI,WAAU;;wBACZ,cAAc,uBACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAGvC,6LAAC,mIAAA,CAAA,OAAI;;sDACH,6LAAC,mIAAA,CAAA,aAAU;;8DACT,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAU;;;;;;8DAC/B,6LAAC,mIAAA,CAAA,kBAAe;8DAAC;;;;;;;;;;;;sDAEnB,6LAAC,mIAAA,CAAA,cAAW;4CAAC,WAAU;sDACrB,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDACJ,aAAY;wDACZ,OAAO;wDACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;wDACzC,WAAU;;;;;;kEAEZ,6LAAC,qIAAA,CAAA,SAAM;wDAAC,SAAS;wDAAc,UAAU,CAAC,OAAO,IAAI;;0EACnD,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;;;;;;;;;;;;8CAOzC,6LAAC,mIAAA,CAAA,OAAI;;sDACH,6LAAC,mIAAA,CAAA,aAAU;;8DACT,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAU;;;;;;8DAC/B,6LAAC,mIAAA,CAAA,kBAAe;8DAAC;;;;;;;;;;;;sDAEnB,6LAAC,mIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,6LAAC,uIAAA,CAAA,WAAQ;oDACP,aAAY;oDACZ,OAAO;oDACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;oDAC3C,MAAM;;;;;;8DAER,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;;gEACb,SAAS,KAAK,CAAC,MAAM,MAAM,CAAC,CAAA,MAAO,IAAI,IAAI,IAAI,MAAM;gEAAC;;;;;;;sEAEzD,6LAAC,qIAAA,CAAA,SAAM;4DAAC,SAAS;4DAAkB,UAAU,CAAC,SAAS,IAAI,MAAM;sEAC9D,2BACC;;kFACE,6LAAC,mNAAA,CAAA,YAAS;wEAAC,WAAU;;;;;;oEAA8B;;6FAIrD;;kFACE,6LAAC,yMAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAWlD,cAAc,0BACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAA6B;;;;;;8DAC3C,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;wCAEtC,cAAc,MAAM,GAAG,mBACtB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAU,MAAK;;sEAC7B,6LAAC,mNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;wDAAiB;wDACnB,cAAc,MAAM;wDAAC;;;;;;;8DAE1C,6LAAC,qIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAc,MAAK;oDAAK,SAAS;;sEAC/C,6LAAC,6MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAAiB;wDACjB,cAAc,MAAM;wDAAC;;;;;;;;;;;;;;;;;;;8CAM/C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6LAAC,oIAAA,CAAA,QAAK;oDACJ,aAAY;oDACZ,OAAO;oDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oDAC9C,WAAU;;;;;;;;;;;;sDAGd,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;;8DACd,6LAAC,6MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;8CAKzC,6LAAC;oCAAI,WAAU;8CACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,6LAAC,mIAAA,CAAA,OAAI;4CAAe,WAAU;sDAC5B,cAAA,6LAAC,mIAAA,CAAA,cAAW;gDAAC,WAAU;0DACrB,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,MAAK;4DACL,SAAS,cAAc,QAAQ,CAAC,KAAK,EAAE;4DACvC,UAAU,IAAM,gBAAgB,KAAK,EAAE;4DACvC,WAAU;;;;;;sEAGZ,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAI,WAAU;;wFACZ,cAAc,KAAK,MAAM;sGAC1B,6LAAC;4FAAG,WAAU;sGAAe,KAAK,KAAK;;;;;;wFACtC,eAAe,KAAK,MAAM;;;;;;;8FAE7B,6LAAC;oFACC,MAAM,KAAK,GAAG;oFACd,QAAO;oFACP,KAAI;oFACJ,WAAU;;wFAET,KAAK,GAAG;sGACT,6LAAC,yNAAA,CAAA,eAAY;4FAAC,WAAU;;;;;;;;;;;;;;;;;;sFAI5B,6LAAC;4EAAI,WAAU;;8FACb,6LAAC,qIAAA,CAAA,SAAM;oFAAC,SAAQ;oFAAQ,MAAK;8FAC3B,cAAA,6LAAC,mMAAA,CAAA,MAAG;wFAAC,WAAU;;;;;;;;;;;8FAEjB,6LAAC,qIAAA,CAAA,SAAM;oFAAC,SAAQ;oFAAQ,MAAK;oFAAK,SAAS,IAAM,cAAc,KAAK,EAAE;8FACpE,cAAA,6LAAC,mNAAA,CAAA,YAAS;wFAAC,WAAU;;;;;;;;;;;8FAEvB,6LAAC,qIAAA,CAAA,SAAM;oFAAC,SAAQ;oFAAQ,MAAK;oFAAK,SAAS,IAAM,aAAa,KAAK,EAAE;8FACnE,cAAA,6LAAC,6MAAA,CAAA,SAAM;wFAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8EAKxB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;;8FACb,6LAAC,6MAAA,CAAA,WAAQ;oFAAC,WAAU;;;;;;gFACnB,KAAK,WAAW,CAAC,kBAAkB;;;;;;;sFAEtC,6LAAC;4EAAI,WAAU;;8FACb,6LAAC,qMAAA,CAAA,OAAI;oFAAC,WAAU;;;;;;gFACf,KAAK,SAAS,CAAC,cAAc;gFAAG;;;;;;;;;;;;;gEAIpC,KAAK,IAAI,CAAC,MAAM,GAAG,mBAClB,6LAAC;oEAAI,WAAU;8EACZ,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,oBACd,6LAAC,oIAAA,CAAA,QAAK;4EAAW,SAAQ;4EAAY,WAAU;sFAC5C;2EADS;;;;;;;;;;;;;;;;;;;;;;;;;;;2CAxDf,KAAK,EAAE;;;;;;;;;;;;;;;;wBAuEzB,cAAc,0BACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAGvC,6LAAC,mIAAA,CAAA,OAAI;8CACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,yMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,6LAAC,oIAAA,CAAA,QAAK;4DACJ,aAAY;4DACZ,WAAU;;;;;;;;;;;;8DAId,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qIAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAU,MAAK;sEAAK;;;;;;sEACpC,6LAAC,qIAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAU,MAAK;sEAAK;;;;;;sEACpC,6LAAC,qIAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAU,MAAK;sEAAK;;;;;;sEACpC,6LAAC,qIAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAU,MAAK;sEAAK;;;;;;sEACpC,6LAAC,qIAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAU,MAAK;sEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAM5C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAc;;;;;;sDAC5B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6LAAC;8DAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrB;GAlbM;KAAA;uCAobS", "debugId": null}}, {"offset": {"line": 1837, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/Node/my-assistant-v3/src/components/ChatGPTInterface.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { useState, FormEvent } from \"react\"\nimport { Paperclip, Mic, CornerDownLeft, MessageSquare, Plus, Settings, User, History, Search, FileText, Database, MoreVertical, Edit2, Trash2 } from \"lucide-react\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { ChatInput, ChatMessageList, Message, Mission } from \"@/components/ui/chat-interface\"\nimport KnowledgeBaseManager from \"@/components/KnowledgeBaseManager\"\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\"\nimport { cn } from \"@/lib/utils\"\n\n// Main Chat Interface Component\nconst ChatGPTInterface: React.FC = () => {\n  const [messages, setMessages] = useState<Message[]>([\n    {\n      id: 1,\n      content: \"Hello! I'm your AI assistant. How can I help you with your mission today?\",\n      sender: \"ai\",\n      timestamp: new Date(),\n    },\n  ])\n\n  const [missions, setMissions] = useState<Mission[]>([\n    {\n      id: 1,\n      title: \"Data Analysis Project\",\n      timestamp: new Date(Date.now() - 86400000),\n      status: \"completed\",\n    },\n    {\n      id: 2,\n      title: \"Market Research\",\n      timestamp: new Date(Date.now() - *********),\n      status: \"completed\",\n    },\n    {\n      id: 3,\n      title: \"Content Strategy\",\n      timestamp: new Date(Date.now() - *********),\n      status: \"in-progress\",\n    },\n  ])\n\n  const [input, setInput] = useState(\"\")\n  const [isLoading, setIsLoading] = useState(false)\n  const [sidebarOpen, setSidebarOpen] = useState(true)\n  const [currentView, setCurrentView] = useState<\"chat\" | \"knowledge\">(\"chat\")\n  const [selectedMissionId, setSelectedMissionId] = useState<number | null>(null)\n\n  const handleSubmit = (e: FormEvent) => {\n    e.preventDefault()\n    if (!input.trim()) return\n\n    const newMessage: Message = {\n      id: messages.length + 1,\n      content: input,\n      sender: \"user\",\n      timestamp: new Date(),\n    }\n\n    setMessages((prev) => [...prev, newMessage])\n    setInput(\"\")\n    setIsLoading(true)\n\n    // Simulate AI response\n    setTimeout(() => {\n      const aiResponse: Message = {\n        id: messages.length + 2,\n        content: \"I understand your request. Let me help you with that mission. I'll analyze the requirements and provide you with a comprehensive solution.\",\n        sender: \"ai\",\n        timestamp: new Date(),\n      }\n      setMessages((prev) => [...prev, aiResponse])\n      setIsLoading(false)\n    }, 1500)\n  }\n\n  const startNewMission = () => {\n    const newMission: Mission = {\n      id: missions.length + 1,\n      title: \"New Mission\",\n      timestamp: new Date(),\n      status: \"in-progress\",\n    }\n    setMissions((prev) => [newMission, ...prev])\n    setSelectedMissionId(newMission.id)\n    setCurrentView(\"chat\")\n    setMessages([\n      {\n        id: 1,\n        content: \"Hello! I'm ready to help you with your new mission. What would you like to accomplish?\",\n        sender: \"ai\",\n        timestamp: new Date(),\n      },\n    ])\n  }\n\n  const selectMission = (missionId: number) => {\n    setSelectedMissionId(missionId)\n    setCurrentView(\"chat\")\n    // Load mission-specific messages here\n    setMessages([\n      {\n        id: 1,\n        content: `Loaded mission ${missionId}. How can I help you continue with this mission?`,\n        sender: \"ai\",\n        timestamp: new Date(),\n      },\n    ])\n  }\n\n  const renameMission = (missionId: number, newTitle: string) => {\n    setMissions(prev =>\n      prev.map(mission =>\n        mission.id === missionId ? { ...mission, title: newTitle } : mission\n      )\n    )\n  }\n\n  const deleteMission = (missionId: number) => {\n    setMissions(prev => prev.filter(mission => mission.id !== missionId))\n    if (selectedMissionId === missionId) {\n      setSelectedMissionId(null)\n      setCurrentView(\"chat\")\n    }\n  }\n\n  return (\n    <div className=\"flex h-screen bg-background text-foreground\">\n      {/* Sidebar */}\n      <div className={cn(\n        \"flex flex-col bg-muted/30 border-r border-border transition-all duration-300\",\n        sidebarOpen ? \"w-80\" : \"w-0 overflow-hidden\"\n      )}>\n        <div className=\"p-4 border-b border-border\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <h2 className=\"text-lg font-semibold\">Mission Control</h2>\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              onClick={() => setSidebarOpen(!sidebarOpen)}\n            >\n              <MessageSquare className=\"h-4 w-4\" />\n            </Button>\n          </div>\n          <div className=\"space-y-2\">\n            <Button\n              onClick={startNewMission}\n              className=\"w-full gap-2\"\n              variant=\"default\"\n            >\n              <Plus className=\"h-4 w-4\" />\n              New Mission\n            </Button>\n            <Button\n              variant={currentView === \"knowledge\" ? \"default\" : \"ghost\"}\n              className=\"w-full justify-start gap-2\"\n              onClick={() => setCurrentView(\"knowledge\")}\n            >\n              <Database className=\"h-4 w-4\" />\n              Knowledge Base\n            </Button>\n          </div>\n        </div>\n\n        <div className=\"flex-1 overflow-y-auto p-4\">\n          <div className=\"mb-4\">\n            <div className=\"relative\">\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n              <input\n                type=\"text\"\n                placeholder=\"Search missions...\"\n                className=\"w-full pl-10 pr-4 py-2 bg-background border border-border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-ring\"\n              />\n            </div>\n          </div>\n\n          <div className=\"space-y-2\">\n            <h3 className=\"text-sm font-medium text-muted-foreground mb-2\">Recent Missions</h3>\n            {missions.map((mission) => (\n              <div\n                key={mission.id}\n                className=\"p-3 rounded-lg bg-background hover:bg-muted/50 cursor-pointer border border-border/50 transition-colors\"\n              >\n                <div className=\"flex items-start justify-between\">\n                  <div className=\"flex-1 min-w-0\">\n                    <h4 className=\"text-sm font-medium truncate\">{mission.title}</h4>\n                    <p className=\"text-xs text-muted-foreground mt-1\">\n                      {mission.timestamp.toLocaleDateString()}\n                    </p>\n                  </div>\n                  <div className={cn(\n                    \"w-2 h-2 rounded-full ml-2 mt-1\",\n                    mission.status === \"completed\" && \"bg-green-500\",\n                    mission.status === \"in-progress\" && \"bg-yellow-500\",\n                    mission.status === \"failed\" && \"bg-red-500\"\n                  )} />\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        <div className=\"p-4 border-t border-border space-y-3\">\n          <div className=\"space-y-1\">\n            <Button\n              variant={currentView === \"chat\" ? \"default\" : \"ghost\"}\n              className=\"w-full justify-start\"\n              onClick={() => setCurrentView(\"chat\")}\n            >\n              <MessageSquare className=\"h-4 w-4 mr-2\" />\n              Mission Chat\n            </Button>\n            <Button\n              variant={currentView === \"knowledge\" ? \"default\" : \"ghost\"}\n              className=\"w-full justify-start\"\n              onClick={() => setCurrentView(\"knowledge\")}\n            >\n              <Database className=\"h-4 w-4 mr-2\" />\n              Knowledge Base\n            </Button>\n          </div>\n\n          <div className=\"flex items-center gap-3 pt-2 border-t border-border\">\n            <div className=\"w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center\">\n              <User className=\"h-4 w-4\" />\n            </div>\n            <div className=\"flex-1 min-w-0\">\n              <p className=\"text-sm font-medium\">User</p>\n              <p className=\"text-xs text-muted-foreground\">Free Plan</p>\n            </div>\n            <Button variant=\"ghost\" size=\"icon\">\n              <Settings className=\"h-4 w-4\" />\n            </Button>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Content */}\n      <div className=\"flex-1 flex flex-col\">\n        {/* Header */}\n        <div className=\"p-4 border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center gap-3\">\n              {!sidebarOpen && (\n                <Button\n                  variant=\"ghost\"\n                  size=\"icon\"\n                  onClick={() => setSidebarOpen(true)}\n                >\n                  <MessageSquare className=\"h-4 w-4\" />\n                </Button>\n              )}\n              <div>\n                <h1 className=\"text-lg font-semibold\">\n                  {currentView === \"chat\" ? \"Current Mission\" : \"Knowledge Base\"}\n                </h1>\n                <p className=\"text-sm text-muted-foreground\">\n                  {currentView === \"chat\" ? \"AI Assistant Ready\" : \"Manage your knowledge resources\"}\n                </p>\n              </div>\n            </div>\n            <div className=\"flex items-center gap-2\">\n              <Button variant=\"ghost\" size=\"icon\">\n                <History className=\"h-4 w-4\" />\n              </Button>\n            </div>\n          </div>\n        </div>\n\n        {/* Main Content Area */}\n        <div className=\"flex-1 overflow-hidden\">\n          {currentView === \"chat\" ? (\n            <ChatMessageList>\n              {messages.map((message) => (\n                <div\n                  key={message.id}\n                  className={cn(\n                    \"flex gap-3 max-w-3xl\",\n                    message.sender === \"user\" ? \"ml-auto flex-row-reverse\" : \"\"\n                  )}\n                >\n                  <div className=\"w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center shrink-0\">\n                    {message.sender === \"user\" ? (\n                      <User className=\"h-4 w-4\" />\n                    ) : (\n                      <MessageSquare className=\"h-4 w-4\" />\n                    )}\n                  </div>\n                  <div\n                    className={cn(\n                      \"rounded-lg px-4 py-3 max-w-[80%]\",\n                      message.sender === \"user\"\n                        ? \"bg-primary text-primary-foreground\"\n                        : \"bg-muted\"\n                    )}\n                  >\n                    <p className=\"text-sm\">{message.content}</p>\n                    <p className=\"text-xs opacity-70 mt-1\">\n                      {message.timestamp.toLocaleTimeString()}\n                    </p>\n                  </div>\n                </div>\n              ))}\n\n              {isLoading && (\n                <div className=\"flex gap-3 max-w-3xl\">\n                  <div className=\"w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center shrink-0\">\n                    <MessageSquare className=\"h-4 w-4\" />\n                  </div>\n                  <div className=\"rounded-lg px-4 py-3 bg-muted\">\n                    <div className=\"flex items-center gap-1\">\n                      <div className=\"w-2 h-2 bg-current rounded-full animate-pulse\" />\n                      <div className=\"w-2 h-2 bg-current rounded-full animate-pulse delay-100\" />\n                      <div className=\"w-2 h-2 bg-current rounded-full animate-pulse delay-200\" />\n                    </div>\n                  </div>\n                </div>\n              )}\n            </ChatMessageList>\n          ) : (\n            <KnowledgeBaseManager />\n          )}\n        </div>\n\n        {/* Input - Only show for chat view */}\n        {currentView === \"chat\" && (\n          <div className=\"p-4 border-t border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\n            <form\n              onSubmit={handleSubmit}\n              className=\"relative rounded-lg border bg-background focus-within:ring-1 focus-within:ring-ring p-1 max-w-4xl mx-auto\"\n            >\n              <ChatInput\n                value={input}\n                onChange={(e) => setInput(e.target.value)}\n                placeholder=\"Describe your mission or ask for help...\"\n                className=\"min-h-12 resize-none rounded-lg bg-background border-0 p-3 shadow-none focus-visible:ring-0\"\n              />\n              <div className=\"flex items-center p-3 pt-0 justify-between\">\n                <div className=\"flex\">\n                  <Button\n                    variant=\"ghost\"\n                    size=\"icon\"\n                    type=\"button\"\n                  >\n                    <Paperclip className=\"size-4\" />\n                  </Button>\n\n                  <Button\n                    variant=\"ghost\"\n                    size=\"icon\"\n                    type=\"button\"\n                  >\n                    <Mic className=\"size-4\" />\n                  </Button>\n                </div>\n                <Button\n                  type=\"submit\"\n                  size=\"sm\"\n                  className=\"ml-auto gap-1.5\"\n                  disabled={!input.trim() || isLoading}\n                >\n                  Send Mission\n                  <CornerDownLeft className=\"size-3.5\" />\n                </Button>\n              </div>\n            </form>\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n\nexport default ChatGPTInterface\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAOA;;;AAdA;;;;;;;AAgBA,gCAAgC;AAChC,MAAM,mBAA6B;;IACjC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa;QAClD;YACE,IAAI;YACJ,SAAS;YACT,QAAQ;YACR,WAAW,IAAI;QACjB;KACD;IAED,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa;QAClD;YACE,IAAI;YACJ,OAAO;YACP,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK;YACjC,QAAQ;QACV;QACA;YACE,IAAI;YACJ,OAAO;YACP,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK;YACjC,QAAQ;QACV;QACA;YACE,IAAI;YACJ,OAAO;YACP,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK;YACjC,QAAQ;QACV;KACD;IAED,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;IACrE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAE1E,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,CAAC,MAAM,IAAI,IAAI;QAEnB,MAAM,aAAsB;YAC1B,IAAI,SAAS,MAAM,GAAG;YACtB,SAAS;YACT,QAAQ;YACR,WAAW,IAAI;QACjB;QAEA,YAAY,CAAC,OAAS;mBAAI;gBAAM;aAAW;QAC3C,SAAS;QACT,aAAa;QAEb,uBAAuB;QACvB,WAAW;YACT,MAAM,aAAsB;gBAC1B,IAAI,SAAS,MAAM,GAAG;gBACtB,SAAS;gBACT,QAAQ;gBACR,WAAW,IAAI;YACjB;YACA,YAAY,CAAC,OAAS;uBAAI;oBAAM;iBAAW;YAC3C,aAAa;QACf,GAAG;IACL;IAEA,MAAM,kBAAkB;QACtB,MAAM,aAAsB;YAC1B,IAAI,SAAS,MAAM,GAAG;YACtB,OAAO;YACP,WAAW,IAAI;YACf,QAAQ;QACV;QACA,YAAY,CAAC,OAAS;gBAAC;mBAAe;aAAK;QAC3C,qBAAqB,WAAW,EAAE;QAClC,eAAe;QACf,YAAY;YACV;gBACE,IAAI;gBACJ,SAAS;gBACT,QAAQ;gBACR,WAAW,IAAI;YACjB;SACD;IACH;IAEA,MAAM,gBAAgB,CAAC;QACrB,qBAAqB;QACrB,eAAe;QACf,sCAAsC;QACtC,YAAY;YACV;gBACE,IAAI;gBACJ,SAAS,CAAC,eAAe,EAAE,UAAU,gDAAgD,CAAC;gBACtF,QAAQ;gBACR,WAAW,IAAI;YACjB;SACD;IACH;IAEA,MAAM,gBAAgB,CAAC,WAAmB;QACxC,YAAY,CAAA,OACV,KAAK,GAAG,CAAC,CAAA,UACP,QAAQ,EAAE,KAAK,YAAY;oBAAE,GAAG,OAAO;oBAAE,OAAO;gBAAS,IAAI;IAGnE;IAEA,MAAM,gBAAgB,CAAC;QACrB,YAAY,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;QAC1D,IAAI,sBAAsB,WAAW;YACnC,qBAAqB;YACrB,eAAe;QACjB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,gFACA,cAAc,SAAS;;kCAEvB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAwB;;;;;;kDACtC,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,eAAe,CAAC;kDAE/B,cAAA,6LAAC,2NAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAG7B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS;wCACT,WAAU;wCACV,SAAQ;;0DAER,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAG9B,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS,gBAAgB,cAAc,YAAY;wCACnD,WAAU;wCACV,SAAS,IAAM,eAAe;;0DAE9B,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;;;;;;;;kCAMtC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,WAAU;;;;;;;;;;;;;;;;;0CAKhB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAiD;;;;;;oCAC9D,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC;4CAEC,WAAU;sDAEV,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAU;0EAAgC,QAAQ,KAAK;;;;;;0EAC3D,6LAAC;gEAAE,WAAU;0EACV,QAAQ,SAAS,CAAC,kBAAkB;;;;;;;;;;;;kEAGzC,6LAAC;wDAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,kCACA,QAAQ,MAAM,KAAK,eAAe,gBAClC,QAAQ,MAAM,KAAK,iBAAiB,iBACpC,QAAQ,MAAM,KAAK,YAAY;;;;;;;;;;;;2CAd9B,QAAQ,EAAE;;;;;;;;;;;;;;;;;kCAsBvB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS,gBAAgB,SAAS,YAAY;wCAC9C,WAAU;wCACV,SAAS,IAAM,eAAe;;0DAE9B,6LAAC,2NAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAG5C,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS,gBAAgB,cAAc,YAAY;wCACnD,WAAU;wCACV,SAAS,IAAM,eAAe;;0DAE9B,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;0CAKzC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;kDAElB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAsB;;;;;;0DACnC,6LAAC;gDAAE,WAAU;0DAAgC;;;;;;;;;;;;kDAE/C,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,MAAK;kDAC3B,cAAA,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO5B,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;wCACZ,CAAC,6BACA,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,eAAe;sDAE9B,cAAA,6LAAC,2NAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;;;;;;sDAG7B,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DACX,gBAAgB,SAAS,oBAAoB;;;;;;8DAEhD,6LAAC;oDAAE,WAAU;8DACV,gBAAgB,SAAS,uBAAuB;;;;;;;;;;;;;;;;;;8CAIvD,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,MAAK;kDAC3B,cAAA,6LAAC,2MAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO3B,6LAAC;wBAAI,WAAU;kCACZ,gBAAgB,uBACf,6LAAC,gJAAA,CAAA,kBAAe;;gCACb,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC;wCAEC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wBACA,QAAQ,MAAM,KAAK,SAAS,6BAA6B;;0DAG3D,6LAAC;gDAAI,WAAU;0DACZ,QAAQ,MAAM,KAAK,uBAClB,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;yEAEhB,6LAAC,2NAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;;;;;;0DAG7B,6LAAC;gDACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oCACA,QAAQ,MAAM,KAAK,SACf,uCACA;;kEAGN,6LAAC;wDAAE,WAAU;kEAAW,QAAQ,OAAO;;;;;;kEACvC,6LAAC;wDAAE,WAAU;kEACV,QAAQ,SAAS,CAAC,kBAAkB;;;;;;;;;;;;;uCAvBpC,QAAQ,EAAE;;;;;gCA6BlB,2BACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,2NAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;;;;;;sDAE3B,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;iDAOzB,6LAAC,6IAAA,CAAA,UAAoB;;;;;;;;;;oBAKxB,gBAAgB,wBACf,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,UAAU;4BACV,WAAU;;8CAEV,6LAAC,gJAAA,CAAA,YAAS;oCACR,OAAO;oCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;oCACxC,aAAY;oCACZ,WAAU;;;;;;8CAEZ,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,MAAK;8DAEL,cAAA,6LAAC,+MAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;;;;;;8DAGvB,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,MAAK;8DAEL,cAAA,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAGnB,6LAAC,qIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,MAAK;4CACL,WAAU;4CACV,UAAU,CAAC,MAAM,IAAI,MAAM;;gDAC5B;8DAEC,6LAAC,iOAAA,CAAA,iBAAc;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS5C;GAxWM;KAAA;uCA0WS", "debugId": null}}]}