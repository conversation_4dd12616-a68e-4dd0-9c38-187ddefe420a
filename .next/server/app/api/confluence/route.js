const CHUNK_PUBLIC_PATH = "server/app/api/confluence/route.js";
const runtime = require("../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/node_modules_next_e8290607._.js");
runtime.loadChunk("server/chunks/node_modules_drizzle-orm_7af76866._.js");
runtime.loadChunk("server/chunks/node_modules_axios_lib_c4a55de8._.js");
runtime.loadChunk("server/chunks/node_modules_mime-db_9ebaabbe._.js");
runtime.loadChunk("server/chunks/node_modules_cheerio_dist_esm_1eebf3ff._.js");
runtime.loadChunk("server/chunks/node_modules_ae677142._.js");
runtime.loadChunk("server/chunks/node_modules_parse5_dist_d1335ed4._.js");
runtime.loadChunk("server/chunks/node_modules_iconv-lite_1d546d44._.js");
runtime.loadChunk("server/chunks/node_modules_undici_bc1cb560._.js");
runtime.loadChunk("server/chunks/node_modules_c6537c8d._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__3213f15c._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/confluence/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/confluence/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/confluence/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
