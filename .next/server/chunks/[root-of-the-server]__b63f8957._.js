module.exports = {

"[project]/.next-internal/server/app/api/missions/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/better-sqlite3 [external] (better-sqlite3, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("better-sqlite3", () => require("better-sqlite3"));

module.exports = mod;
}}),
"[externals]/node:crypto [external] (node:crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:crypto", () => require("node:crypto"));

module.exports = mod;
}}),
"[project]/src/lib/db/schema.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "executionLogs": (()=>executionLogs),
    "knowledgeBase": (()=>knowledgeBase),
    "missions": (()=>missions),
    "plans": (()=>plans),
    "reflections": (()=>reflections),
    "tasks": (()=>tasks),
    "toolUsage": (()=>toolUsage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$table$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/drizzle-orm/sqlite-core/table.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/drizzle-orm/sqlite-core/columns/text.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$integer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/drizzle-orm/sqlite-core/columns/integer.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$real$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/drizzle-orm/sqlite-core/columns/real.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$paralleldrive$2f$cuid2$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@paralleldrive/cuid2/index.js [app-route] (ecmascript)");
;
;
const missions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$table$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["sqliteTable"])('missions', {
    id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])('id').primaryKey().$defaultFn(()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$paralleldrive$2f$cuid2$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createId"])()),
    title: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])('title').notNull(),
    description: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])('description').notNull(),
    status: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])('status', {
        enum: [
            'pending',
            'planning',
            'executing',
            'completed',
            'failed'
        ]
    }).notNull().default('pending'),
    priority: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])('priority', {
        enum: [
            'low',
            'medium',
            'high',
            'urgent'
        ]
    }).notNull().default('medium'),
    createdAt: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$integer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["integer"])('created_at', {
        mode: 'timestamp'
    }).notNull().$defaultFn(()=>new Date()),
    updatedAt: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$integer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["integer"])('updated_at', {
        mode: 'timestamp'
    }).notNull().$defaultFn(()=>new Date()),
    completedAt: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$integer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["integer"])('completed_at', {
        mode: 'timestamp'
    }),
    metadata: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])('metadata', {
        mode: 'json'
    }).$type()
});
const plans = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$table$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["sqliteTable"])('plans', {
    id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])('id').primaryKey().$defaultFn(()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$paralleldrive$2f$cuid2$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createId"])()),
    missionId: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])('mission_id').notNull().references(()=>missions.id, {
        onDelete: 'cascade'
    }),
    version: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$integer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["integer"])('version').notNull().default(1),
    title: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])('title').notNull(),
    description: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])('description'),
    status: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])('status', {
        enum: [
            'draft',
            'active',
            'completed',
            'failed',
            'superseded'
        ]
    }).notNull().default('draft'),
    estimatedDuration: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$integer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["integer"])('estimated_duration'),
    actualDuration: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$integer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["integer"])('actual_duration'),
    createdAt: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$integer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["integer"])('created_at', {
        mode: 'timestamp'
    }).notNull().$defaultFn(()=>new Date()),
    updatedAt: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$integer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["integer"])('updated_at', {
        mode: 'timestamp'
    }).notNull().$defaultFn(()=>new Date()),
    metadata: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])('metadata', {
        mode: 'json'
    }).$type()
});
const tasks = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$table$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["sqliteTable"])('tasks', {
    id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])('id').primaryKey().$defaultFn(()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$paralleldrive$2f$cuid2$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createId"])()),
    planId: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])('plan_id').notNull().references(()=>plans.id, {
        onDelete: 'cascade'
    }),
    parentTaskId: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])('parent_task_id').references(()=>tasks.id),
    title: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])('title').notNull(),
    description: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])('description'),
    status: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])('status', {
        enum: [
            'pending',
            'in_progress',
            'completed',
            'failed',
            'skipped'
        ]
    }).notNull().default('pending'),
    priority: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$integer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["integer"])('priority').notNull().default(0),
    toolName: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])('tool_name'),
    toolParams: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])('tool_params', {
        mode: 'json'
    }).$type(),
    dependencies: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])('dependencies', {
        mode: 'json'
    }).$type(),
    estimatedDuration: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$integer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["integer"])('estimated_duration'),
    actualDuration: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$integer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["integer"])('actual_duration'),
    startedAt: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$integer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["integer"])('started_at', {
        mode: 'timestamp'
    }),
    completedAt: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$integer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["integer"])('completed_at', {
        mode: 'timestamp'
    }),
    createdAt: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$integer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["integer"])('created_at', {
        mode: 'timestamp'
    }).notNull().$defaultFn(()=>new Date()),
    updatedAt: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$integer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["integer"])('updated_at', {
        mode: 'timestamp'
    }).notNull().$defaultFn(()=>new Date()),
    result: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])('result', {
        mode: 'json'
    }).$type(),
    metadata: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])('metadata', {
        mode: 'json'
    }).$type()
});
const executionLogs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$table$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["sqliteTable"])('execution_logs', {
    id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])('id').primaryKey().$defaultFn(()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$paralleldrive$2f$cuid2$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createId"])()),
    missionId: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])('mission_id').references(()=>missions.id, {
        onDelete: 'cascade'
    }),
    planId: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])('plan_id').references(()=>plans.id, {
        onDelete: 'cascade'
    }),
    taskId: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])('task_id').references(()=>tasks.id, {
        onDelete: 'cascade'
    }),
    level: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])('level', {
        enum: [
            'debug',
            'info',
            'warn',
            'error'
        ]
    }).notNull().default('info'),
    message: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])('message').notNull(),
    data: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])('data', {
        mode: 'json'
    }).$type(),
    timestamp: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$integer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["integer"])('timestamp', {
        mode: 'timestamp'
    }).notNull().$defaultFn(()=>new Date())
});
const reflections = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$table$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["sqliteTable"])('reflections', {
    id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])('id').primaryKey().$defaultFn(()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$paralleldrive$2f$cuid2$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createId"])()),
    missionId: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])('mission_id').references(()=>missions.id, {
        onDelete: 'cascade'
    }),
    planId: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])('plan_id').references(()=>plans.id, {
        onDelete: 'cascade'
    }),
    taskId: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])('task_id').references(()=>tasks.id, {
        onDelete: 'cascade'
    }),
    type: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])('type', {
        enum: [
            'progress_assessment',
            'plan_optimization',
            'error_analysis',
            'success_analysis'
        ]
    }).notNull(),
    content: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])('content').notNull(),
    insights: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])('insights', {
        mode: 'json'
    }).$type(),
    recommendations: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])('recommendations', {
        mode: 'json'
    }).$type(),
    confidence: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$real$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["real"])('confidence'),
    createdAt: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$integer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["integer"])('created_at', {
        mode: 'timestamp'
    }).notNull().$defaultFn(()=>new Date()),
    metadata: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])('metadata', {
        mode: 'json'
    }).$type()
});
const knowledgeBase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$table$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["sqliteTable"])('knowledge_base', {
    id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])('id').primaryKey().$defaultFn(()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$paralleldrive$2f$cuid2$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createId"])()),
    sourceType: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])('source_type', {
        enum: [
            'confluence',
            'web',
            'document',
            'manual'
        ]
    }).notNull(),
    sourceUrl: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])('source_url'),
    title: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])('title').notNull(),
    content: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])('content').notNull(),
    summary: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])('summary'),
    tags: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])('tags', {
        mode: 'json'
    }).$type(),
    embedding: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])('embedding', {
        mode: 'json'
    }).$type(),
    lastIndexed: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$integer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["integer"])('last_indexed', {
        mode: 'timestamp'
    }).notNull().$defaultFn(()=>new Date()),
    createdAt: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$integer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["integer"])('created_at', {
        mode: 'timestamp'
    }).notNull().$defaultFn(()=>new Date()),
    metadata: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])('metadata', {
        mode: 'json'
    }).$type()
});
const toolUsage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$table$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["sqliteTable"])('tool_usage', {
    id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])('id').primaryKey().$defaultFn(()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$paralleldrive$2f$cuid2$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createId"])()),
    taskId: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])('task_id').references(()=>tasks.id, {
        onDelete: 'cascade'
    }),
    toolName: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])('tool_name').notNull(),
    parameters: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])('parameters', {
        mode: 'json'
    }).$type(),
    result: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])('result', {
        mode: 'json'
    }).$type(),
    success: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$integer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["integer"])('success', {
        mode: 'boolean'
    }).notNull(),
    duration: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$integer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["integer"])('duration'),
    errorMessage: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])('error_message'),
    timestamp: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$integer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["integer"])('timestamp', {
        mode: 'timestamp'
    }).notNull().$defaultFn(()=>new Date())
});
}}),
"[externals]/path [external] (path, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/node:fs [external] (node:fs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:fs", () => require("node:fs"));

module.exports = mod;
}}),
"[project]/src/lib/db/index.ts [app-route] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "db": (()=>db),
    "default": (()=>__TURBOPACK__default__export__),
    "initializeDatabase": (()=>initializeDatabase)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$better$2d$sqlite3$2f$driver$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/drizzle-orm/better-sqlite3/driver.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$better$2d$sqlite3__$5b$external$5d$__$28$better$2d$sqlite3$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/better-sqlite3 [external] (better-sqlite3, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/db/schema.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$better$2d$sqlite3$2f$migrator$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/drizzle-orm/better-sqlite3/migrator.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/fs [external] (fs, cjs)");
;
;
;
;
;
;
// Ensure data directory exists
const dataDir = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(process.cwd(), 'data');
if (!__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(dataDir)) {
    __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].mkdirSync(dataDir, {
        recursive: true
    });
}
// Create database connection
const sqlite = new __TURBOPACK__imported__module__$5b$externals$5d2f$better$2d$sqlite3__$5b$external$5d$__$28$better$2d$sqlite3$2c$__cjs$29$__["default"](__TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(dataDir, 'agent.db'));
sqlite.pragma('journal_mode = WAL');
const db = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$better$2d$sqlite3$2f$driver$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["drizzle"])(sqlite, {
    schema: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__
});
async function initializeDatabase() {
    try {
        // Run migrations
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$better$2d$sqlite3$2f$migrator$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["migrate"])(db, {
            migrationsFolder: __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(process.cwd(), 'drizzle')
        });
        console.log('Database initialized successfully');
    } catch (error) {
        console.error('Failed to initialize database:', error);
        throw error;
    }
}
;
const __TURBOPACK__default__export__ = db;
}}),
"[project]/src/lib/db/index.ts [app-route] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$externals$5d2f$better$2d$sqlite3__$5b$external$5d$__$28$better$2d$sqlite3$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/better-sqlite3 [external] (better-sqlite3, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/db/schema.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/fs [external] (fs, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/db/index.ts [app-route] (ecmascript) <locals>");
}}),
"[project]/src/lib/db/schema.ts [app-route] (ecmascript) <export * as schema>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "schema": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/db/schema.ts [app-route] (ecmascript)");
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}}),
"[externals]/tty [external] (tty, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tty", () => require("tty"));

module.exports = mod;
}}),
"[externals]/os [external] (os, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("os", () => require("os"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[project]/src/lib/agent/deepseek.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "DeepSeekClient": (()=>DeepSeekClient)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-route] (ecmascript)");
;
class DeepSeekClient {
    client;
    config;
    constructor(config){
        this.config = config;
        this.client = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].create({
            baseURL: config.deepseekBaseUrl || 'https://api.deepseek.com/v1',
            headers: {
                'Authorization': `Bearer ${config.deepseekApiKey}`,
                'Content-Type': 'application/json'
            },
            timeout: config.timeout || 30000
        });
    }
    async chat(messages, options = {}) {
        try {
            const response = await this.client.post('/chat/completions', {
                model: options.model || 'deepseek-reasoner',
                messages,
                temperature: options.temperature || 0.7,
                max_tokens: options.maxTokens || 2000,
                stream: options.stream || false
            });
            return response.data;
        } catch (error) {
            if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].isAxiosError(error)) {
                throw new Error(`DeepSeek API error: ${error.response?.data?.error?.message || error.message}`);
            }
            throw error;
        }
    }
    async generateEmbedding(text, model = 'deepseek-embedding') {
        try {
            const response = await this.client.post('/embeddings', {
                model,
                input: text
            });
            const embeddingResponse = response.data;
            return embeddingResponse.data[0].embedding;
        } catch (error) {
            if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].isAxiosError(error)) {
                throw new Error(`DeepSeek Embedding API error: ${error.response?.data?.error?.message || error.message}`);
            }
            throw error;
        }
    }
    async generatePlan(mission, context) {
        const messages = [
            {
                role: 'system',
                content: `You are an expert service management AI agent with advanced planning capabilities. Your task is to create detailed, executable plans for missions.

Key principles:
1. Break down complex missions into clear, actionable tasks
2. Consider dependencies between tasks
3. Estimate realistic timeframes
4. Identify required tools and resources
5. Plan for potential risks and contingencies
6. Ensure each task has clear success criteria

Return your response as a structured JSON plan with the following format:
{
  "title": "Plan title",
  "description": "Plan description",
  "estimatedDuration": 120,
  "tasks": [
    {
      "title": "Task title",
      "description": "Task description",
      "priority": 0,
      "toolName": "tool_name",
      "toolParams": {},
      "dependencies": [],
      "estimatedDuration": 30
    }
  ],
  "reasoning": "Explanation of the planning approach",
  "confidence": 0.85
}`
            },
            {
                role: 'user',
                content: `Mission: ${mission}${context ? `\n\nContext: ${context}` : ''}`
            }
        ];
        const response = await this.chat(messages, {
            temperature: 0.3
        });
        return response.choices[0].message.content;
    }
    async reflect(type, data) {
        const systemPrompts = {
            progress_assessment: 'You are analyzing the progress of a mission execution. Assess what has been completed, what remains, and any issues that need attention.',
            plan_optimization: 'You are optimizing an execution plan based on current progress and learnings. Suggest improvements to increase efficiency and success probability.',
            error_analysis: 'You are analyzing a failure or error that occurred during mission execution. Identify root causes and suggest corrective actions.',
            success_analysis: 'You are analyzing a successful task or mission completion. Extract insights and best practices for future use.'
        };
        const messages = [
            {
                role: 'system',
                content: `${systemPrompts[type]}

Return your analysis as a structured JSON response:
{
  "insights": ["insight 1", "insight 2"],
  "recommendations": ["recommendation 1", "recommendation 2"],
  "confidence": 0.8,
  "reasoning": "Detailed explanation of your analysis"
}`
            },
            {
                role: 'user',
                content: JSON.stringify(data, null, 2)
            }
        ];
        const response = await this.chat(messages, {
            temperature: 0.4
        });
        return response.choices[0].message.content;
    }
    async selectTool(task, availableTools) {
        const messages = [
            {
                role: 'system',
                content: `You are selecting the most appropriate tool for a given task. Consider the task requirements and available tools.

Available tools: ${availableTools.join(', ')}

Return your response as JSON:
{
  "selectedTool": "tool_name",
  "reasoning": "Why this tool is most appropriate",
  "confidence": 0.9
}`
            },
            {
                role: 'user',
                content: `Task: ${JSON.stringify(task, null, 2)}`
            }
        ];
        const response = await this.chat(messages, {
            temperature: 0.2
        });
        return response.choices[0].message.content;
    }
    async assessCompletion(mission, result) {
        const messages = [
            {
                role: 'system',
                content: `You are assessing whether a mission has been successfully completed based on the original goals and the execution results.

Return your assessment as JSON:
{
  "completed": true/false,
  "completionPercentage": 0.95,
  "reasoning": "Detailed explanation",
  "remainingWork": ["item 1", "item 2"],
  "confidence": 0.9
}`
            },
            {
                role: 'user',
                content: `Mission: ${JSON.stringify(mission, null, 2)}\n\nResult: ${JSON.stringify(result, null, 2)}`
            }
        ];
        const response = await this.chat(messages, {
            temperature: 0.3
        });
        return response.choices[0].message.content;
    }
}
}}),
"[project]/src/lib/agent/tools/base.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "BaseTool": (()=>BaseTool),
    "ToolRegistry": (()=>ToolRegistry)
});
class BaseTool {
    createResult(success, data, error, metadata) {
        return {
            success,
            data,
            error,
            metadata: {
                ...metadata,
                toolName: this.name,
                timestamp: new Date().toISOString()
            }
        };
    }
    validateParams(params, required) {
        for (const param of required){
            if (!(param in params) || params[param] === undefined || params[param] === null) {
                throw new Error(`Missing required parameter: ${param}`);
            }
        }
    }
    async withRetry(operation, maxRetries = 3, delay = 1000) {
        let lastError;
        for(let attempt = 1; attempt <= maxRetries; attempt++){
            try {
                return await operation();
            } catch (error) {
                lastError = error;
                if (attempt === maxRetries) {
                    throw lastError;
                }
                // Exponential backoff
                await new Promise((resolve)=>setTimeout(resolve, delay * Math.pow(2, attempt - 1)));
            }
        }
        throw lastError;
    }
}
class ToolRegistry {
    tools = new Map();
    register(tool) {
        this.tools.set(tool.name, tool);
    }
    get(name) {
        return this.tools.get(name);
    }
    getAll() {
        return Array.from(this.tools.values());
    }
    getNames() {
        return Array.from(this.tools.keys());
    }
    has(name) {
        return this.tools.has(name);
    }
}
}}),
"[externals]/node:stream [external] (node:stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:stream", () => require("node:stream"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[externals]/string_decoder [external] (string_decoder, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("string_decoder", () => require("string_decoder"));

module.exports = mod;
}}),
"[externals]/node:assert [external] (node:assert, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:assert", () => require("node:assert"));

module.exports = mod;
}}),
"[externals]/node:net [external] (node:net, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:net", () => require("node:net"));

module.exports = mod;
}}),
"[externals]/node:http [external] (node:http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:http", () => require("node:http"));

module.exports = mod;
}}),
"[externals]/node:buffer [external] (node:buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:buffer", () => require("node:buffer"));

module.exports = mod;
}}),
"[externals]/node:querystring [external] (node:querystring, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:querystring", () => require("node:querystring"));

module.exports = mod;
}}),
"[externals]/node:events [external] (node:events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:events", () => require("node:events"));

module.exports = mod;
}}),
"[externals]/node:diagnostics_channel [external] (node:diagnostics_channel, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:diagnostics_channel", () => require("node:diagnostics_channel"));

module.exports = mod;
}}),
"[externals]/node:util [external] (node:util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:util", () => require("node:util"));

module.exports = mod;
}}),
"[externals]/node:tls [external] (node:tls, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:tls", () => require("node:tls"));

module.exports = mod;
}}),
"[externals]/node:zlib [external] (node:zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:zlib", () => require("node:zlib"));

module.exports = mod;
}}),
"[externals]/node:perf_hooks [external] (node:perf_hooks, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:perf_hooks", () => require("node:perf_hooks"));

module.exports = mod;
}}),
"[externals]/node:util/types [external] (node:util/types, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:util/types", () => require("node:util/types"));

module.exports = mod;
}}),
"[externals]/node:worker_threads [external] (node:worker_threads, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:worker_threads", () => require("node:worker_threads"));

module.exports = mod;
}}),
"[externals]/node:http2 [external] (node:http2, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:http2", () => require("node:http2"));

module.exports = mod;
}}),
"[externals]/node:url [external] (node:url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:url", () => require("node:url"));

module.exports = mod;
}}),
"[externals]/node:async_hooks [external] (node:async_hooks, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:async_hooks", () => require("node:async_hooks"));

module.exports = mod;
}}),
"[externals]/node:console [external] (node:console, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:console", () => require("node:console"));

module.exports = mod;
}}),
"[externals]/node:dns [external] (node:dns, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:dns", () => require("node:dns"));

module.exports = mod;
}}),
"[project]/src/lib/agent/tools/confluence.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ConfluenceLoaderTool": (()=>ConfluenceLoaderTool),
    "ConfluenceSearchTool": (()=>ConfluenceSearchTool)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$agent$2f$tools$2f$base$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/agent/tools/base.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$cheerio$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/cheerio/dist/esm/index.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$cheerio$2f$dist$2f$esm$2f$load$2d$parse$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/cheerio/dist/esm/load-parse.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/db/index.ts [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/db/index.ts [app-route] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__schema$3e$__ = __turbopack_context__.i("[project]/src/lib/db/schema.ts [app-route] (ecmascript) <export * as schema>");
// Import eq from drizzle-orm
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/drizzle-orm/sql/expressions/conditions.js [app-route] (ecmascript)");
;
;
;
;
class ConfluenceLoaderTool extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$agent$2f$tools$2f$base$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["BaseTool"] {
    name = 'confluence_loader';
    description = 'Load and index content from Confluence pages';
    parameters = {
        url: {
            type: 'string',
            required: true,
            description: 'Confluence page URL'
        },
        includeAttachments: {
            type: 'boolean',
            required: false,
            description: 'Whether to include attachments'
        }
    };
    deepseek;
    config;
    constructor(deepseek, config = {}){
        super();
        this.deepseek = deepseek;
        this.config = config;
    }
    async execute(params, context) {
        try {
            this.validateParams(params, [
                'url'
            ]);
            const { url, includeAttachments = false } = params;
            context?.logger.info(`Loading Confluence page: ${url}`);
            // Extract content from the page
            const content = await this.extractPageContent(url);
            // Generate embedding for the content
            const embedding = await this.deepseek.generateEmbedding(content.text);
            // Store in knowledge base
            const knowledgeEntry = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].insert(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__schema$3e$__["schema"].knowledgeBase).values({
                sourceType: 'confluence',
                sourceUrl: url,
                title: content.title,
                content: content.text,
                summary: content.summary,
                tags: content.tags,
                embedding,
                metadata: {
                    includeAttachments,
                    wordCount: content.text.split(' ').length,
                    extractedAt: new Date().toISOString()
                }
            }).returning();
            context?.logger.info(`Successfully indexed Confluence page: ${content.title}`);
            return this.createResult(true, {
                id: knowledgeEntry[0].id,
                title: content.title,
                wordCount: content.text.split(' ').length,
                url
            });
        } catch (error) {
            context?.logger.error(`Failed to load Confluence page: ${error}`);
            return this.createResult(false, null, error.message);
        }
    }
    async extractPageContent(url) {
        try {
            // For demo purposes, we'll use web scraping
            // In production, you'd use the Confluence REST API
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(url, {
                headers: {
                    'User-Agent': 'Mozilla/5.0 (compatible; ServiceAgent/1.0)'
                },
                timeout: 10000
            });
            const $ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$cheerio$2f$dist$2f$esm$2f$load$2d$parse$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["load"])(response.data);
            // Extract title
            const title = $('title').text().trim() || $('h1').first().text().trim() || 'Untitled';
            // Extract main content
            let text = '';
            // Try common Confluence content selectors
            const contentSelectors = [
                '#main-content',
                '.wiki-content',
                '.page-content',
                'main',
                '.content',
                'article'
            ];
            for (const selector of contentSelectors){
                const content = $(selector);
                if (content.length > 0) {
                    // Remove script and style elements
                    content.find('script, style, nav, header, footer').remove();
                    text = content.text().trim();
                    break;
                }
            }
            // Fallback to body content if no specific content area found
            if (!text) {
                $('script, style, nav, header, footer').remove();
                text = $('body').text().trim();
            }
            // Clean up the text
            text = text.replace(/\s+/g, ' ').trim();
            // Generate summary (first 200 words)
            const words = text.split(' ');
            const summary = words.slice(0, 200).join(' ') + (words.length > 200 ? '...' : '');
            // Extract potential tags from headings and meta tags
            const tags = [];
            // From headings
            $('h1, h2, h3').each((_, el)=>{
                const heading = $(el).text().trim();
                if (heading && heading.length < 50) {
                    tags.push(heading.toLowerCase());
                }
            });
            // From meta keywords
            const metaKeywords = $('meta[name="keywords"]').attr('content');
            if (metaKeywords) {
                tags.push(...metaKeywords.split(',').map((tag)=>tag.trim().toLowerCase()));
            }
            // Remove duplicates and limit to 10 tags
            const uniqueTags = [
                ...new Set(tags)
            ].slice(0, 10);
            return {
                title,
                text,
                summary,
                tags: uniqueTags
            };
        } catch (error) {
            throw new Error(`Failed to extract content from ${url}: ${error.message}`);
        }
    }
}
class ConfluenceSearchTool extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$agent$2f$tools$2f$base$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["BaseTool"] {
    name = 'confluence_search';
    description = 'Search indexed Confluence content using natural language';
    parameters = {
        query: {
            type: 'string',
            required: true,
            description: 'Natural language search query'
        },
        limit: {
            type: 'number',
            required: false,
            description: 'Maximum number of results to return'
        }
    };
    deepseek;
    constructor(deepseek){
        super();
        this.deepseek = deepseek;
    }
    async execute(params, context) {
        try {
            this.validateParams(params, [
                'query'
            ]);
            const { query, limit = 5 } = params;
            context?.logger.info(`Searching Confluence content for: ${query}`);
            // Generate embedding for the search query
            const queryEmbedding = await this.deepseek.generateEmbedding(query);
            // Search for similar content using vector similarity
            // Note: This is a simplified implementation. In production, you'd use a proper vector database
            const allContent = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].select().from(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__schema$3e$__["schema"].knowledgeBase).where((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__schema$3e$__["schema"].knowledgeBase.sourceType, 'confluence'));
            // Calculate cosine similarity for each document
            const results = allContent.map((doc)=>({
                    ...doc,
                    similarity: this.cosineSimilarity(queryEmbedding, doc.embedding || [])
                })).filter((doc)=>doc.similarity > 0.3) // Minimum similarity threshold
            .sort((a, b)=>b.similarity - a.similarity).slice(0, limit);
            context?.logger.info(`Found ${results.length} relevant documents`);
            return this.createResult(true, {
                query,
                results: results.map((doc)=>({
                        id: doc.id,
                        title: doc.title,
                        summary: doc.summary,
                        url: doc.sourceUrl,
                        similarity: doc.similarity,
                        tags: doc.tags
                    })),
                totalFound: results.length
            });
        } catch (error) {
            context?.logger.error(`Failed to search Confluence content: ${error}`);
            return this.createResult(false, null, error.message);
        }
    }
    cosineSimilarity(a, b) {
        if (a.length !== b.length) return 0;
        let dotProduct = 0;
        let normA = 0;
        let normB = 0;
        for(let i = 0; i < a.length; i++){
            dotProduct += a[i] * b[i];
            normA += a[i] * a[i];
            normB += b[i] * b[i];
        }
        return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
    }
}
;
}}),
"[project]/src/lib/agent/core.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "AgentLogger": (()=>AgentLogger),
    "AutonomousEngine": (()=>AutonomousEngine),
    "GoalOrientedEngine": (()=>GoalOrientedEngine),
    "PlanningEngine": (()=>PlanningEngine),
    "ReflectiveEngine": (()=>ReflectiveEngine),
    "ServiceManagementAgentImpl": (()=>ServiceManagementAgentImpl),
    "ToolUseEngine": (()=>ToolUseEngine)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$agent$2f$deepseek$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/agent/deepseek.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$agent$2f$tools$2f$base$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/agent/tools/base.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$agent$2f$tools$2f$confluence$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/agent/tools/confluence.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/db/index.ts [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/db/index.ts [app-route] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__schema$3e$__ = __turbopack_context__.i("[project]/src/lib/db/schema.ts [app-route] (ecmascript) <export * as schema>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/drizzle-orm/sql/expressions/conditions.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$node$2d$cache$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/node-cache/index.js [app-route] (ecmascript)");
;
;
;
;
;
;
class AgentLogger {
    context;
    constructor(context){
        this.context = context;
    }
    async log(level, message, data) {
        console.log(`[${level.toUpperCase()}] ${message}`, data ? JSON.stringify(data, null, 2) : '');
        // Store in database
        try {
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].insert(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__schema$3e$__["schema"].executionLogs).values({
                missionId: this.context.missionId,
                planId: this.context.planId,
                taskId: this.context.taskId,
                level,
                message,
                data
            });
        } catch (error) {
            console.error('Failed to store log:', error);
        }
    }
    debug(message, data) {
        this.log('debug', message, data);
    }
    info(message, data) {
        this.log('info', message, data);
    }
    warn(message, data) {
        this.log('warn', message, data);
    }
    error(message, data) {
        this.log('error', message, data);
    }
}
class PlanningEngine {
    deepseek;
    constructor(deepseek){
        this.deepseek = deepseek;
    }
    async createPlan(mission, context) {
        context.logger.info(`Creating plan for mission: ${mission.title}`);
        try {
            const planResponse = await this.deepseek.generatePlan(`${mission.title}\n\n${mission.description}`, `Priority: ${mission.priority}`);
            const planData = JSON.parse(planResponse);
            // Create plan in database
            const [newPlan] = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].insert(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__schema$3e$__["schema"].plans).values({
                missionId: mission.id,
                title: planData.title,
                description: planData.description,
                estimatedDuration: planData.estimatedDuration,
                status: 'draft'
            }).returning();
            // Create tasks
            const tasks = [];
            for (const taskData of planData.tasks){
                const [newTask] = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].insert(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__schema$3e$__["schema"].tasks).values({
                    planId: newPlan.id,
                    title: taskData.title,
                    description: taskData.description,
                    priority: taskData.priority,
                    toolName: taskData.toolName,
                    toolParams: taskData.toolParams,
                    dependencies: taskData.dependencies,
                    estimatedDuration: taskData.estimatedDuration
                }).returning();
                tasks.push({
                    ...newTask,
                    createdAt: new Date(newTask.createdAt),
                    updatedAt: new Date(newTask.updatedAt)
                });
            }
            const plan = {
                ...newPlan,
                tasks,
                createdAt: new Date(newPlan.createdAt),
                updatedAt: new Date(newPlan.updatedAt)
            };
            return {
                plan,
                reasoning: planData.reasoning,
                confidence: planData.confidence
            };
        } catch (error) {
            context.logger.error(`Failed to create plan: ${error}`);
            throw error;
        }
    }
    async updatePlan(plan, feedback, context) {
        context.logger.info(`Updating plan: ${plan.title}`);
        try {
            const updateResponse = await this.deepseek.generatePlan(`Update this plan based on feedback:\n\nOriginal Plan: ${JSON.stringify(plan, null, 2)}\n\nFeedback: ${feedback}`);
            const updatedData = JSON.parse(updateResponse);
            // Update plan in database
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].update(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__schema$3e$__["schema"].plans).set({
                title: updatedData.title,
                description: updatedData.description,
                estimatedDuration: updatedData.estimatedDuration,
                version: plan.version + 1,
                updatedAt: new Date()
            }).where((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__schema$3e$__["schema"].plans.id, plan.id));
            // Update tasks (simplified - in production, you'd handle this more carefully)
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].delete(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__schema$3e$__["schema"].tasks).where((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__schema$3e$__["schema"].tasks.planId, plan.id));
            const tasks = [];
            for (const taskData of updatedData.tasks){
                const [newTask] = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].insert(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__schema$3e$__["schema"].tasks).values({
                    planId: plan.id,
                    title: taskData.title,
                    description: taskData.description,
                    priority: taskData.priority,
                    toolName: taskData.toolName,
                    toolParams: taskData.toolParams,
                    dependencies: taskData.dependencies,
                    estimatedDuration: taskData.estimatedDuration
                }).returning();
                tasks.push({
                    ...newTask,
                    createdAt: new Date(newTask.createdAt),
                    updatedAt: new Date(newTask.updatedAt)
                });
            }
            const updatedPlan = {
                ...plan,
                title: updatedData.title,
                description: updatedData.description,
                estimatedDuration: updatedData.estimatedDuration,
                version: plan.version + 1,
                tasks,
                updatedAt: new Date()
            };
            return {
                plan: updatedPlan,
                reasoning: updatedData.reasoning,
                confidence: updatedData.confidence
            };
        } catch (error) {
            context.logger.error(`Failed to update plan: ${error}`);
            throw error;
        }
    }
    async decomposeTasks(task, context) {
        context.logger.info(`Decomposing task: ${task.title}`);
        try {
            const decompositionResponse = await this.deepseek.generatePlan(`Decompose this task into smaller subtasks:\n\n${JSON.stringify(task, null, 2)}`);
            const decompositionData = JSON.parse(decompositionResponse);
            const subtasks = [];
            for (const subtaskData of decompositionData.tasks){
                const [newSubtask] = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].insert(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__schema$3e$__["schema"].tasks).values({
                    planId: task.planId,
                    parentTaskId: task.id,
                    title: subtaskData.title,
                    description: subtaskData.description,
                    priority: subtaskData.priority,
                    toolName: subtaskData.toolName,
                    toolParams: subtaskData.toolParams,
                    dependencies: subtaskData.dependencies,
                    estimatedDuration: subtaskData.estimatedDuration
                }).returning();
                subtasks.push({
                    ...newSubtask,
                    createdAt: new Date(newSubtask.createdAt),
                    updatedAt: new Date(newSubtask.updatedAt)
                });
            }
            return subtasks;
        } catch (error) {
            context.logger.error(`Failed to decompose task: ${error}`);
            throw error;
        }
    }
}
class ToolUseEngine {
    deepseek;
    toolRegistry;
    constructor(deepseek, toolRegistry){
        this.deepseek = deepseek;
        this.toolRegistry = toolRegistry;
    }
    getAvailableTools() {
        return this.toolRegistry.getAll();
    }
    async selectTool(task, context) {
        if (task.toolName) {
            return this.toolRegistry.get(task.toolName) || null;
        }
        try {
            const selectionResponse = await this.deepseek.selectTool(task, this.toolRegistry.getNames());
            const selectionData = JSON.parse(selectionResponse);
            return this.toolRegistry.get(selectionData.selectedTool) || null;
        } catch (error) {
            context.logger.error(`Failed to select tool: ${error}`);
            return null;
        }
    }
    async executeTool(tool, params, context) {
        context.logger.info(`Executing tool: ${tool.name}`);
        const startTime = Date.now();
        try {
            const result = await tool.execute(params, context);
            const duration = Date.now() - startTime;
            // Log tool usage
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].insert(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__schema$3e$__["schema"].toolUsage).values({
                taskId: context.taskId,
                toolName: tool.name,
                parameters: params,
                result: result.data,
                success: result.success,
                duration,
                errorMessage: result.error
            });
            return result;
        } catch (error) {
            const duration = Date.now() - startTime;
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].insert(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__schema$3e$__["schema"].toolUsage).values({
                taskId: context.taskId,
                toolName: tool.name,
                parameters: params,
                result: null,
                success: false,
                duration,
                errorMessage: error.message
            });
            throw error;
        }
    }
}
class AutonomousEngine {
    toolUse;
    deepseek;
    constructor(toolUse, deepseek){
        this.toolUse = toolUse;
        this.deepseek = deepseek;
    }
    async executeTask(task, context) {
        context.logger.info(`Executing task: ${task.title}`);
        try {
            // Update task status to in_progress
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].update(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__schema$3e$__["schema"].tasks).set({
                status: 'in_progress',
                startedAt: new Date()
            }).where((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__schema$3e$__["schema"].tasks.id, task.id));
            // Select appropriate tool
            const tool = await this.toolUse.selectTool(task, context);
            if (!tool) {
                throw new Error(`No suitable tool found for task: ${task.title}`);
            }
            // Execute the tool
            const result = await this.toolUse.executeTool(tool, task.toolParams || {}, {
                ...context,
                taskId: task.id
            });
            // Update task status based on result
            const status = result.success ? 'completed' : 'failed';
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].update(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__schema$3e$__["schema"].tasks).set({
                status,
                completedAt: new Date(),
                result: result.data
            }).where((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__schema$3e$__["schema"].tasks.id, task.id));
            return result;
        } catch (error) {
            context.logger.error(`Task execution failed: ${error}`);
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].update(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__schema$3e$__["schema"].tasks).set({
                status: 'failed',
                completedAt: new Date(),
                result: {
                    error: error.message
                }
            }).where((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__schema$3e$__["schema"].tasks.id, task.id));
            throw error;
        }
    }
    async executePlan(plan, context) {
        context.logger.info(`Executing plan: ${plan.title}`);
        const completedTasks = [];
        const failedTasks = [];
        const reflections = [];
        try {
            // Update plan status to active
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].update(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__schema$3e$__["schema"].plans).set({
                status: 'active'
            }).where((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__schema$3e$__["schema"].plans.id, plan.id));
            // Execute tasks in priority order, respecting dependencies
            const sortedTasks = this.sortTasksByPriorityAndDependencies(plan.tasks);
            for (const task of sortedTasks){
                try {
                    // Check if dependencies are completed
                    if (task.dependencies && task.dependencies.length > 0) {
                        const dependencyStatuses = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].select().from(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__schema$3e$__["schema"].tasks).where((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["and"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__schema$3e$__["schema"].tasks.planId, plan.id)));
                        const incompleteDeps = task.dependencies.filter((depId)=>!dependencyStatuses.find((t)=>t.id === depId && t.status === 'completed'));
                        if (incompleteDeps.length > 0) {
                            context.logger.warn(`Skipping task ${task.title} due to incomplete dependencies`);
                            continue;
                        }
                    }
                    const result = await this.executeTask(task, context);
                    if (result.success) {
                        completedTasks.push(task);
                    } else {
                        failedTasks.push(task);
                    }
                } catch (error) {
                    context.logger.error(`Task failed: ${task.title}`, error);
                    failedTasks.push(task);
                }
            }
            // Update plan status
            const finalStatus = failedTasks.length === 0 ? 'completed' : 'failed';
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].update(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__schema$3e$__["schema"].plans).set({
                status: finalStatus
            }).where((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__schema$3e$__["schema"].plans.id, plan.id));
            return {
                success: failedTasks.length === 0,
                completedTasks,
                failedTasks,
                reflections
            };
        } catch (error) {
            context.logger.error(`Plan execution failed: ${error}`);
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].update(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__schema$3e$__["schema"].plans).set({
                status: 'failed'
            }).where((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__schema$3e$__["schema"].plans.id, plan.id));
            return {
                success: false,
                completedTasks,
                failedTasks,
                reflections,
                error: error.message
            };
        }
    }
    async handleError(error, context) {
        context.logger.error(`Handling error: ${error.message}`);
        // Store error analysis
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].insert(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__schema$3e$__["schema"].reflections).values({
            missionId: context.missionId,
            planId: context.planId,
            taskId: context.taskId,
            type: 'error_analysis',
            content: error.message,
            insights: [
                `Error occurred: ${error.message}`
            ],
            recommendations: [
                'Review task parameters',
                'Check tool availability'
            ],
            confidence: 0.8
        });
    }
    sortTasksByPriorityAndDependencies(tasks) {
        // Simple topological sort with priority consideration
        const sorted = [];
        const visited = new Set();
        const visiting = new Set();
        const visit = (task)=>{
            if (visiting.has(task.id)) {
                throw new Error(`Circular dependency detected involving task: ${task.title}`);
            }
            if (visited.has(task.id)) return;
            visiting.add(task.id);
            // Visit dependencies first
            if (task.dependencies) {
                for (const depId of task.dependencies){
                    const depTask = tasks.find((t)=>t.id === depId);
                    if (depTask) {
                        visit(depTask);
                    }
                }
            }
            visiting.delete(task.id);
            visited.add(task.id);
            sorted.push(task);
        };
        // Sort by priority first, then visit
        const prioritySorted = [
            ...tasks
        ].sort((a, b)=>a.priority - b.priority);
        for (const task of prioritySorted){
            visit(task);
        }
        return sorted;
    }
}
class ReflectiveEngine {
    deepseek;
    constructor(deepseek){
        this.deepseek = deepseek;
    }
    async assessProgress(plan, context) {
        context.logger.info(`Assessing progress for plan: ${plan.title}`);
        try {
            // Get current task statuses
            const tasks = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].select().from(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__schema$3e$__["schema"].tasks).where((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__schema$3e$__["schema"].tasks.planId, plan.id));
            const completedTasks = tasks.filter((t)=>t.status === 'completed');
            const failedTasks = tasks.filter((t)=>t.status === 'failed');
            const inProgressTasks = tasks.filter((t)=>t.status === 'in_progress');
            const progressData = {
                plan,
                totalTasks: tasks.length,
                completedTasks: completedTasks.length,
                failedTasks: failedTasks.length,
                inProgressTasks: inProgressTasks.length,
                progressPercentage: tasks.length > 0 ? completedTasks.length / tasks.length * 100 : 0
            };
            const reflectionResponse = await this.deepseek.reflect('progress_assessment', progressData);
            const reflectionData = JSON.parse(reflectionResponse);
            // Store reflection
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].insert(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__schema$3e$__["schema"].reflections).values({
                missionId: context.missionId,
                planId: context.planId,
                type: 'progress_assessment',
                content: reflectionData.reasoning,
                insights: reflectionData.insights,
                recommendations: reflectionData.recommendations,
                confidence: reflectionData.confidence
            });
            return {
                insights: reflectionData.insights,
                recommendations: reflectionData.recommendations,
                confidence: reflectionData.confidence
            };
        } catch (error) {
            context.logger.error(`Failed to assess progress: ${error}`);
            throw error;
        }
    }
    async analyzeFailure(task, error, context) {
        context.logger.info(`Analyzing failure for task: ${task.title}`);
        try {
            const failureData = {
                task,
                error
            };
            const reflectionResponse = await this.deepseek.reflect('error_analysis', failureData);
            const reflectionData = JSON.parse(reflectionResponse);
            // Store reflection
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].insert(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__schema$3e$__["schema"].reflections).values({
                missionId: context.missionId,
                planId: context.planId,
                taskId: task.id,
                type: 'error_analysis',
                content: reflectionData.reasoning,
                insights: reflectionData.insights,
                recommendations: reflectionData.recommendations,
                confidence: reflectionData.confidence
            });
            return {
                insights: reflectionData.insights,
                recommendations: reflectionData.recommendations,
                confidence: reflectionData.confidence
            };
        } catch (error) {
            context.logger.error(`Failed to analyze failure: ${error}`);
            throw error;
        }
    }
    async optimizePlan(plan, context) {
        context.logger.info(`Optimizing plan: ${plan.title}`);
        try {
            // Get execution history and performance data
            const tasks = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].select().from(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__schema$3e$__["schema"].tasks).where((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__schema$3e$__["schema"].tasks.planId, plan.id));
            const toolUsage = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].select().from(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__schema$3e$__["schema"].toolUsage).where((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__schema$3e$__["schema"].toolUsage.taskId, tasks[0]?.id || ''));
            const optimizationData = {
                plan,
                tasks,
                toolUsage
            };
            const reflectionResponse = await this.deepseek.reflect('plan_optimization', optimizationData);
            const reflectionData = JSON.parse(reflectionResponse);
            // Store reflection
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].insert(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__schema$3e$__["schema"].reflections).values({
                missionId: context.missionId,
                planId: context.planId,
                type: 'plan_optimization',
                content: reflectionData.reasoning,
                insights: reflectionData.insights,
                recommendations: reflectionData.recommendations,
                confidence: reflectionData.confidence
            });
            return {
                insights: reflectionData.insights,
                recommendations: reflectionData.recommendations,
                confidence: reflectionData.confidence
            };
        } catch (error) {
            context.logger.error(`Failed to optimize plan: ${error}`);
            throw error;
        }
    }
}
class GoalOrientedEngine {
    deepseek;
    constructor(deepseek){
        this.deepseek = deepseek;
    }
    async trackProgress(mission, context) {
        context.logger.info(`Tracking progress for mission: ${mission.title}`);
        try {
            // Get all plans for this mission
            const plans = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].select().from(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__schema$3e$__["schema"].plans).where((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__schema$3e$__["schema"].plans.missionId, mission.id));
            if (plans.length === 0) return 0;
            // Get all tasks for all plans
            const allTasks = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].select().from(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__schema$3e$__["schema"].tasks).where((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__schema$3e$__["schema"].tasks.planId, plans[0].id)); // Simplified for demo
            const completedTasks = allTasks.filter((t)=>t.status === 'completed');
            const progress = allTasks.length > 0 ? completedTasks.length / allTasks.length : 0;
            return Math.min(progress, 1.0);
        } catch (error) {
            context.logger.error(`Failed to track progress: ${error}`);
            return 0;
        }
    }
    async validateCompletion(mission, result, context) {
        context.logger.info(`Validating completion for mission: ${mission.title}`);
        try {
            const assessmentResponse = await this.deepseek.assessCompletion(mission, result);
            const assessmentData = JSON.parse(assessmentResponse);
            return assessmentData.completed && assessmentData.completionPercentage >= 0.9;
        } catch (error) {
            context.logger.error(`Failed to validate completion: ${error}`);
            return false;
        }
    }
    async adjustStrategy(mission, plan, context) {
        context.logger.info(`Adjusting strategy for mission: ${mission.title}`);
        try {
            // Get current progress and performance data
            const progress = await this.trackProgress(mission, context);
            if (progress < 0.5) {
                // If progress is low, consider plan adjustments
                const adjustmentData = {
                    mission,
                    plan,
                    progress
                };
                const adjustmentResponse = await this.deepseek.generatePlan(`Adjust this plan to improve progress:\n\n${JSON.stringify(adjustmentData, null, 2)}`);
                const adjustmentPlan = JSON.parse(adjustmentResponse);
                // Create new plan version
                const [newPlan] = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].insert(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__schema$3e$__["schema"].plans).values({
                    missionId: mission.id,
                    version: plan.version + 1,
                    title: adjustmentPlan.title,
                    description: adjustmentPlan.description,
                    estimatedDuration: adjustmentPlan.estimatedDuration,
                    status: 'draft'
                }).returning();
                // Mark old plan as superseded
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].update(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__schema$3e$__["schema"].plans).set({
                    status: 'superseded'
                }).where((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__schema$3e$__["schema"].plans.id, plan.id));
                return {
                    ...newPlan,
                    tasks: [],
                    createdAt: new Date(newPlan.createdAt),
                    updatedAt: new Date(newPlan.updatedAt)
                };
            }
            return plan; // No adjustment needed
        } catch (error) {
            context.logger.error(`Failed to adjust strategy: ${error}`);
            return plan;
        }
    }
}
class ServiceManagementAgentImpl {
    planning;
    toolUse;
    autonomous;
    reflective;
    goalOriented;
    deepseek;
    toolRegistry;
    cache;
    constructor(config){
        this.deepseek = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$agent$2f$deepseek$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DeepSeekClient"](config);
        this.toolRegistry = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$agent$2f$tools$2f$base$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ToolRegistry"]();
        this.cache = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$node$2d$cache$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"]({
            stdTTL: 3600
        }); // 1 hour cache
        // Initialize capabilities
        this.planning = new PlanningEngine(this.deepseek);
        this.toolUse = new ToolUseEngine(this.deepseek, this.toolRegistry);
        this.autonomous = new AutonomousEngine(this.toolUse, this.deepseek);
        this.reflective = new ReflectiveEngine(this.deepseek);
        this.goalOriented = new GoalOrientedEngine(this.deepseek);
        // Register default tools
        this.registerDefaultTools();
    }
    registerDefaultTools() {
        this.toolRegistry.register(new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$agent$2f$tools$2f$confluence$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ConfluenceLoaderTool"](this.deepseek));
        this.toolRegistry.register(new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$agent$2f$tools$2f$confluence$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ConfluenceSearchTool"](this.deepseek));
    }
    async executeMission(mission) {
        const logger = new AgentLogger({
            missionId: mission.id
        });
        logger.info(`Starting mission execution: ${mission.title}`);
        try {
            // Update mission status
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].update(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__schema$3e$__["schema"].missions).set({
                status: 'planning'
            }).where((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__schema$3e$__["schema"].missions.id, mission.id));
            // Create execution context
            const context = {
                missionId: mission.id,
                planId: '',
                tools: new Map(this.toolRegistry.getAll().map((tool)=>[
                        tool.name,
                        tool
                    ])),
                cache: this.cache,
                logger
            };
            // Create initial plan
            const planningResult = await this.planning.createPlan(mission, context);
            context.planId = planningResult.plan.id;
            // Update mission status to executing
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].update(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__schema$3e$__["schema"].missions).set({
                status: 'executing'
            }).where((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__schema$3e$__["schema"].missions.id, mission.id));
            // Execute the plan
            const executionResult = await this.autonomous.executePlan(planningResult.plan, context);
            // Assess final completion
            const isCompleted = await this.goalOriented.validateCompletion(mission, executionResult, context);
            // Update mission status
            const finalStatus = isCompleted ? 'completed' : 'failed';
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].update(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__schema$3e$__["schema"].missions).set({
                status: finalStatus,
                completedAt: isCompleted ? new Date() : undefined
            }).where((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__schema$3e$__["schema"].missions.id, mission.id));
            logger.info(`Mission ${isCompleted ? 'completed' : 'failed'}: ${mission.title}`);
            return {
                ...executionResult,
                success: isCompleted
            };
        } catch (error) {
            logger.error(`Mission execution failed: ${error}`);
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].update(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__schema$3e$__["schema"].missions).set({
                status: 'failed'
            }).where((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__schema$3e$__["schema"].missions.id, mission.id));
            return {
                success: false,
                completedTasks: [],
                failedTasks: [],
                reflections: [],
                error: error.message
            };
        }
    }
    async pauseMission(missionId) {
        // Implementation for pausing mission
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].update(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__schema$3e$__["schema"].missions).set({
            status: 'pending'
        }).where((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__schema$3e$__["schema"].missions.id, missionId));
    }
    async resumeMission(missionId) {
        // Implementation for resuming mission
        const mission = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].select().from(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__schema$3e$__["schema"].missions).where((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__schema$3e$__["schema"].missions.id, missionId)).limit(1);
        if (mission.length > 0) {
            await this.executeMission({
                ...mission[0],
                createdAt: new Date(mission[0].createdAt),
                updatedAt: new Date(mission[0].updatedAt),
                completedAt: mission[0].completedAt ? new Date(mission[0].completedAt) : undefined
            });
        }
    }
    async cancelMission(missionId) {
        // Implementation for canceling mission
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].update(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__schema$3e$__["schema"].missions).set({
            status: 'failed'
        }).where((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__schema$3e$__["schema"].missions.id, missionId));
    }
}
}}),
"[project]/src/app/api/missions/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/db/index.ts [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/db/index.ts [app-route] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__schema$3e$__ = __turbopack_context__.i("[project]/src/lib/db/schema.ts [app-route] (ecmascript) <export * as schema>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$agent$2f$core$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/agent/core.ts [app-route] (ecmascript)");
;
;
;
// Initialize agent (in production, this would be a singleton)
const agent = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$agent$2f$core$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ServiceManagementAgentImpl"]({
    deepseekApiKey: process.env.DEEPSEEK_API_KEY || '',
    debug: ("TURBOPACK compile-time value", "development") === 'development'
});
async function GET() {
    try {
        const missions = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].select().from(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__schema$3e$__["schema"].missions).orderBy(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__schema$3e$__["schema"].missions.createdAt);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: missions.map((mission)=>({
                    ...mission,
                    createdAt: mission.createdAt.toISOString(),
                    updatedAt: mission.updatedAt.toISOString(),
                    completedAt: mission.completedAt?.toISOString()
                }))
        });
    } catch (error) {
        console.error('Failed to fetch missions:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: 'Failed to fetch missions'
        }, {
            status: 500
        });
    }
}
async function POST(request) {
    try {
        const body = await request.json();
        const { title, description, priority = 'medium' } = body;
        if (!title || !description) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: 'Title and description are required'
            }, {
                status: 400
            });
        }
        // Create mission in database
        const [mission] = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].insert(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__schema$3e$__["schema"].missions).values({
            title,
            description,
            priority,
            status: 'pending'
        }).returning();
        // Start mission execution asynchronously
        const missionData = {
            ...mission,
            createdAt: new Date(mission.createdAt),
            updatedAt: new Date(mission.updatedAt),
            completedAt: mission.completedAt ? new Date(mission.completedAt) : undefined
        };
        // Execute mission in background
        agent.executeMission(missionData).catch((error)=>{
            console.error(`Mission execution failed for ${mission.id}:`, error);
        });
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: {
                ...mission,
                createdAt: mission.createdAt.toISOString(),
                updatedAt: mission.updatedAt.toISOString(),
                completedAt: mission.completedAt?.toISOString()
            }
        });
    } catch (error) {
        console.error('Failed to create mission:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: 'Failed to create mission'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__b63f8957._.js.map