{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/Node/my-assistant-v3/src/lib/db/schema.ts"], "sourcesContent": ["import { sqliteTable, text, integer, real } from 'drizzle-orm/sqlite-core';\nimport { createId } from '@paralleldrive/cuid2';\n\n// Missions table - stores user missions/goals\nexport const missions = sqliteTable('missions', {\n  id: text('id').primaryKey().$defaultFn(() => createId()),\n  title: text('title').notNull(),\n  description: text('description').notNull(),\n  status: text('status', { enum: ['pending', 'planning', 'executing', 'completed', 'failed'] }).notNull().default('pending'),\n  priority: text('priority', { enum: ['low', 'medium', 'high', 'urgent'] }).notNull().default('medium'),\n  createdAt: integer('created_at', { mode: 'timestamp' }).notNull().$defaultFn(() => new Date()),\n  updatedAt: integer('updated_at', { mode: 'timestamp' }).notNull().$defaultFn(() => new Date()),\n  completedAt: integer('completed_at', { mode: 'timestamp' }),\n  metadata: text('metadata', { mode: 'json' }).$type<Record<string, any>>(),\n});\n\n// Plans table - stores decomposed plans for missions\nexport const plans = sqliteTable('plans', {\n  id: text('id').primaryKey().$defaultFn(() => createId()),\n  missionId: text('mission_id').notNull().references(() => missions.id, { onDelete: 'cascade' }),\n  version: integer('version').notNull().default(1),\n  title: text('title').notNull(),\n  description: text('description'),\n  status: text('status', { enum: ['draft', 'active', 'completed', 'failed', 'superseded'] }).notNull().default('draft'),\n  estimatedDuration: integer('estimated_duration'), // in minutes\n  actualDuration: integer('actual_duration'), // in minutes\n  createdAt: integer('created_at', { mode: 'timestamp' }).notNull().$defaultFn(() => new Date()),\n  updatedAt: integer('updated_at', { mode: 'timestamp' }).notNull().$defaultFn(() => new Date()),\n  metadata: text('metadata', { mode: 'json' }).$type<Record<string, any>>(),\n});\n\n// Tasks table - stores individual tasks within plans\nexport const tasks = sqliteTable('tasks', {\n  id: text('id').primaryKey().$defaultFn(() => createId()),\n  planId: text('plan_id').notNull().references(() => plans.id, { onDelete: 'cascade' }),\n  parentTaskId: text('parent_task_id').references(() => tasks.id),\n  title: text('title').notNull(),\n  description: text('description'),\n  status: text('status', { enum: ['pending', 'in_progress', 'completed', 'failed', 'skipped'] }).notNull().default('pending'),\n  priority: integer('priority').notNull().default(0), // 0 = highest priority\n  toolName: text('tool_name'), // which tool to use for this task\n  toolParams: text('tool_params', { mode: 'json' }).$type<Record<string, any>>(),\n  dependencies: text('dependencies', { mode: 'json' }).$type<string[]>(), // task IDs this depends on\n  estimatedDuration: integer('estimated_duration'), // in minutes\n  actualDuration: integer('actual_duration'), // in minutes\n  startedAt: integer('started_at', { mode: 'timestamp' }),\n  completedAt: integer('completed_at', { mode: 'timestamp' }),\n  createdAt: integer('created_at', { mode: 'timestamp' }).notNull().$defaultFn(() => new Date()),\n  updatedAt: integer('updated_at', { mode: 'timestamp' }).notNull().$defaultFn(() => new Date()),\n  result: text('result', { mode: 'json' }).$type<Record<string, any>>(),\n  metadata: text('metadata', { mode: 'json' }).$type<Record<string, any>>(),\n});\n\n// Execution logs table - stores detailed execution history\nexport const executionLogs = sqliteTable('execution_logs', {\n  id: text('id').primaryKey().$defaultFn(() => createId()),\n  missionId: text('mission_id').references(() => missions.id, { onDelete: 'cascade' }),\n  planId: text('plan_id').references(() => plans.id, { onDelete: 'cascade' }),\n  taskId: text('task_id').references(() => tasks.id, { onDelete: 'cascade' }),\n  level: text('level', { enum: ['debug', 'info', 'warn', 'error'] }).notNull().default('info'),\n  message: text('message').notNull(),\n  data: text('data', { mode: 'json' }).$type<Record<string, any>>(),\n  timestamp: integer('timestamp', { mode: 'timestamp' }).notNull().$defaultFn(() => new Date()),\n});\n\n// Reflections table - stores agent reflections and learnings\nexport const reflections = sqliteTable('reflections', {\n  id: text('id').primaryKey().$defaultFn(() => createId()),\n  missionId: text('mission_id').references(() => missions.id, { onDelete: 'cascade' }),\n  planId: text('plan_id').references(() => plans.id, { onDelete: 'cascade' }),\n  taskId: text('task_id').references(() => tasks.id, { onDelete: 'cascade' }),\n  type: text('type', { enum: ['progress_assessment', 'plan_optimization', 'error_analysis', 'success_analysis'] }).notNull(),\n  content: text('content').notNull(),\n  insights: text('insights', { mode: 'json' }).$type<string[]>(),\n  recommendations: text('recommendations', { mode: 'json' }).$type<string[]>(),\n  confidence: real('confidence'), // 0.0 to 1.0\n  createdAt: integer('created_at', { mode: 'timestamp' }).notNull().$defaultFn(() => new Date()),\n  metadata: text('metadata', { mode: 'json' }).$type<Record<string, any>>(),\n});\n\n// Knowledge base table - stores indexed content from Confluence and other sources\nexport const knowledgeBase = sqliteTable('knowledge_base', {\n  id: text('id').primaryKey().$defaultFn(() => createId()),\n  sourceType: text('source_type', { enum: ['confluence', 'web', 'document', 'manual'] }).notNull(),\n  sourceUrl: text('source_url'),\n  title: text('title').notNull(),\n  content: text('content').notNull(),\n  summary: text('summary'),\n  tags: text('tags', { mode: 'json' }).$type<string[]>(),\n  embedding: text('embedding', { mode: 'json' }).$type<number[]>(), // vector embedding\n  lastIndexed: integer('last_indexed', { mode: 'timestamp' }).notNull().$defaultFn(() => new Date()),\n  createdAt: integer('created_at', { mode: 'timestamp' }).notNull().$defaultFn(() => new Date()),\n  metadata: text('metadata', { mode: 'json' }).$type<Record<string, any>>(),\n});\n\n// Tool usage tracking\nexport const toolUsage = sqliteTable('tool_usage', {\n  id: text('id').primaryKey().$defaultFn(() => createId()),\n  taskId: text('task_id').references(() => tasks.id, { onDelete: 'cascade' }),\n  toolName: text('tool_name').notNull(),\n  parameters: text('parameters', { mode: 'json' }).$type<Record<string, any>>(),\n  result: text('result', { mode: 'json' }).$type<Record<string, any>>(),\n  success: integer('success', { mode: 'boolean' }).notNull(),\n  duration: integer('duration'), // in milliseconds\n  errorMessage: text('error_message'),\n  timestamp: integer('timestamp', { mode: 'timestamp' }).notNull().$defaultFn(() => new Date()),\n});\n\nexport type Mission = typeof missions.$inferSelect;\nexport type NewMission = typeof missions.$inferInsert;\nexport type Plan = typeof plans.$inferSelect;\nexport type NewPlan = typeof plans.$inferInsert;\nexport type Task = typeof tasks.$inferSelect;\nexport type NewTask = typeof tasks.$inferInsert;\nexport type ExecutionLog = typeof executionLogs.$inferSelect;\nexport type NewExecutionLog = typeof executionLogs.$inferInsert;\nexport type Reflection = typeof reflections.$inferSelect;\nexport type NewReflection = typeof reflections.$inferInsert;\nexport type KnowledgeBase = typeof knowledgeBase.$inferSelect;\nexport type NewKnowledgeBase = typeof knowledgeBase.$inferInsert;\nexport type ToolUsage = typeof toolUsage.$inferSelect;\nexport type NewToolUsage = typeof toolUsage.$inferInsert;\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAAA;AAAA;AAAA;AACA;;;AAGO,MAAM,WAAW,CAAA,GAAA,2JAAA,CAAA,cAAW,AAAD,EAAE,YAAY;IAC9C,IAAI,CAAA,GAAA,qKAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU,GAAG,UAAU,CAAC,IAAM,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IACpD,OAAO,CAAA,GAAA,qKAAA,CAAA,OAAI,AAAD,EAAE,SAAS,OAAO;IAC5B,aAAa,CAAA,GAAA,qKAAA,CAAA,OAAI,AAAD,EAAE,eAAe,OAAO;IACxC,QAAQ,CAAA,GAAA,qKAAA,CAAA,OAAI,AAAD,EAAE,UAAU;QAAE,MAAM;YAAC;YAAW;YAAY;YAAa;YAAa;SAAS;IAAC,GAAG,OAAO,GAAG,OAAO,CAAC;IAChH,UAAU,CAAA,GAAA,qKAAA,CAAA,OAAI,AAAD,EAAE,YAAY;QAAE,MAAM;YAAC;YAAO;YAAU;YAAQ;SAAS;IAAC,GAAG,OAAO,GAAG,OAAO,CAAC;IAC5F,WAAW,CAAA,GAAA,wKAAA,CAAA,UAAO,AAAD,EAAE,cAAc;QAAE,MAAM;IAAY,GAAG,OAAO,GAAG,UAAU,CAAC,IAAM,IAAI;IACvF,WAAW,CAAA,GAAA,wKAAA,CAAA,UAAO,AAAD,EAAE,cAAc;QAAE,MAAM;IAAY,GAAG,OAAO,GAAG,UAAU,CAAC,IAAM,IAAI;IACvF,aAAa,CAAA,GAAA,wKAAA,CAAA,UAAO,AAAD,EAAE,gBAAgB;QAAE,MAAM;IAAY;IACzD,UAAU,CAAA,GAAA,qKAAA,CAAA,OAAI,AAAD,EAAE,YAAY;QAAE,MAAM;IAAO,GAAG,KAAK;AACpD;AAGO,MAAM,QAAQ,CAAA,GAAA,2JAAA,CAAA,cAAW,AAAD,EAAE,SAAS;IACxC,IAAI,CAAA,GAAA,qKAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU,GAAG,UAAU,CAAC,IAAM,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IACpD,WAAW,CAAA,GAAA,qKAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO,GAAG,UAAU,CAAC,IAAM,SAAS,EAAE,EAAE;QAAE,UAAU;IAAU;IAC5F,SAAS,CAAA,GAAA,wKAAA,CAAA,UAAO,AAAD,EAAE,WAAW,OAAO,GAAG,OAAO,CAAC;IAC9C,OAAO,CAAA,GAAA,qKAAA,CAAA,OAAI,AAAD,EAAE,SAAS,OAAO;IAC5B,aAAa,CAAA,GAAA,qKAAA,CAAA,OAAI,AAAD,EAAE;IAClB,QAAQ,CAAA,GAAA,qKAAA,CAAA,OAAI,AAAD,EAAE,UAAU;QAAE,MAAM;YAAC;YAAS;YAAU;YAAa;YAAU;SAAa;IAAC,GAAG,OAAO,GAAG,OAAO,CAAC;IAC7G,mBAAmB,CAAA,GAAA,wKAAA,CAAA,UAAO,AAAD,EAAE;IAC3B,gBAAgB,CAAA,GAAA,wKAAA,CAAA,UAAO,AAAD,EAAE;IACxB,WAAW,CAAA,GAAA,wKAAA,CAAA,UAAO,AAAD,EAAE,cAAc;QAAE,MAAM;IAAY,GAAG,OAAO,GAAG,UAAU,CAAC,IAAM,IAAI;IACvF,WAAW,CAAA,GAAA,wKAAA,CAAA,UAAO,AAAD,EAAE,cAAc;QAAE,MAAM;IAAY,GAAG,OAAO,GAAG,UAAU,CAAC,IAAM,IAAI;IACvF,UAAU,CAAA,GAAA,qKAAA,CAAA,OAAI,AAAD,EAAE,YAAY;QAAE,MAAM;IAAO,GAAG,KAAK;AACpD;AAGO,MAAM,QAAQ,CAAA,GAAA,2JAAA,CAAA,cAAW,AAAD,EAAE,SAAS;IACxC,IAAI,CAAA,GAAA,qKAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU,GAAG,UAAU,CAAC,IAAM,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IACpD,QAAQ,CAAA,GAAA,qKAAA,CAAA,OAAI,AAAD,EAAE,WAAW,OAAO,GAAG,UAAU,CAAC,IAAM,MAAM,EAAE,EAAE;QAAE,UAAU;IAAU;IACnF,cAAc,CAAA,GAAA,qKAAA,CAAA,OAAI,AAAD,EAAE,kBAAkB,UAAU,CAAC,IAAM,MAAM,EAAE;IAC9D,OAAO,CAAA,GAAA,qKAAA,CAAA,OAAI,AAAD,EAAE,SAAS,OAAO;IAC5B,aAAa,CAAA,GAAA,qKAAA,CAAA,OAAI,AAAD,EAAE;IAClB,QAAQ,CAAA,GAAA,qKAAA,CAAA,OAAI,AAAD,EAAE,UAAU;QAAE,MAAM;YAAC;YAAW;YAAe;YAAa;YAAU;SAAU;IAAC,GAAG,OAAO,GAAG,OAAO,CAAC;IACjH,UAAU,CAAA,GAAA,wKAAA,CAAA,UAAO,AAAD,EAAE,YAAY,OAAO,GAAG,OAAO,CAAC;IAChD,UAAU,CAAA,GAAA,qKAAA,CAAA,OAAI,AAAD,EAAE;IACf,YAAY,CAAA,GAAA,qKAAA,CAAA,OAAI,AAAD,EAAE,eAAe;QAAE,MAAM;IAAO,GAAG,KAAK;IACvD,cAAc,CAAA,GAAA,qKAAA,CAAA,OAAI,AAAD,EAAE,gBAAgB;QAAE,MAAM;IAAO,GAAG,KAAK;IAC1D,mBAAmB,CAAA,GAAA,wKAAA,CAAA,UAAO,AAAD,EAAE;IAC3B,gBAAgB,CAAA,GAAA,wKAAA,CAAA,UAAO,AAAD,EAAE;IACxB,WAAW,CAAA,GAAA,wKAAA,CAAA,UAAO,AAAD,EAAE,cAAc;QAAE,MAAM;IAAY;IACrD,aAAa,CAAA,GAAA,wKAAA,CAAA,UAAO,AAAD,EAAE,gBAAgB;QAAE,MAAM;IAAY;IACzD,WAAW,CAAA,GAAA,wKAAA,CAAA,UAAO,AAAD,EAAE,cAAc;QAAE,MAAM;IAAY,GAAG,OAAO,GAAG,UAAU,CAAC,IAAM,IAAI;IACvF,WAAW,CAAA,GAAA,wKAAA,CAAA,UAAO,AAAD,EAAE,cAAc;QAAE,MAAM;IAAY,GAAG,OAAO,GAAG,UAAU,CAAC,IAAM,IAAI;IACvF,QAAQ,CAAA,GAAA,qKAAA,CAAA,OAAI,AAAD,EAAE,UAAU;QAAE,MAAM;IAAO,GAAG,KAAK;IAC9C,UAAU,CAAA,GAAA,qKAAA,CAAA,OAAI,AAAD,EAAE,YAAY;QAAE,MAAM;IAAO,GAAG,KAAK;AACpD;AAGO,MAAM,gBAAgB,CAAA,GAAA,2JAAA,CAAA,cAAW,AAAD,EAAE,kBAAkB;IACzD,IAAI,CAAA,GAAA,qKAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU,GAAG,UAAU,CAAC,IAAM,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IACpD,WAAW,CAAA,GAAA,qKAAA,CAAA,OAAI,AAAD,EAAE,cAAc,UAAU,CAAC,IAAM,SAAS,EAAE,EAAE;QAAE,UAAU;IAAU;IAClF,QAAQ,CAAA,GAAA,qKAAA,CAAA,OAAI,AAAD,EAAE,WAAW,UAAU,CAAC,IAAM,MAAM,EAAE,EAAE;QAAE,UAAU;IAAU;IACzE,QAAQ,CAAA,GAAA,qKAAA,CAAA,OAAI,AAAD,EAAE,WAAW,UAAU,CAAC,IAAM,MAAM,EAAE,EAAE;QAAE,UAAU;IAAU;IACzE,OAAO,CAAA,GAAA,qKAAA,CAAA,OAAI,AAAD,EAAE,SAAS;QAAE,MAAM;YAAC;YAAS;YAAQ;YAAQ;SAAQ;IAAC,GAAG,OAAO,GAAG,OAAO,CAAC;IACrF,SAAS,CAAA,GAAA,qKAAA,CAAA,OAAI,AAAD,EAAE,WAAW,OAAO;IAChC,MAAM,CAAA,GAAA,qKAAA,CAAA,OAAI,AAAD,EAAE,QAAQ;QAAE,MAAM;IAAO,GAAG,KAAK;IAC1C,WAAW,CAAA,GAAA,wKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QAAE,MAAM;IAAY,GAAG,OAAO,GAAG,UAAU,CAAC,IAAM,IAAI;AACxF;AAGO,MAAM,cAAc,CAAA,GAAA,2JAAA,CAAA,cAAW,AAAD,EAAE,eAAe;IACpD,IAAI,CAAA,GAAA,qKAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU,GAAG,UAAU,CAAC,IAAM,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IACpD,WAAW,CAAA,GAAA,qKAAA,CAAA,OAAI,AAAD,EAAE,cAAc,UAAU,CAAC,IAAM,SAAS,EAAE,EAAE;QAAE,UAAU;IAAU;IAClF,QAAQ,CAAA,GAAA,qKAAA,CAAA,OAAI,AAAD,EAAE,WAAW,UAAU,CAAC,IAAM,MAAM,EAAE,EAAE;QAAE,UAAU;IAAU;IACzE,QAAQ,CAAA,GAAA,qKAAA,CAAA,OAAI,AAAD,EAAE,WAAW,UAAU,CAAC,IAAM,MAAM,EAAE,EAAE;QAAE,UAAU;IAAU;IACzE,MAAM,CAAA,GAAA,qKAAA,CAAA,OAAI,AAAD,EAAE,QAAQ;QAAE,MAAM;YAAC;YAAuB;YAAqB;YAAkB;SAAmB;IAAC,GAAG,OAAO;IACxH,SAAS,CAAA,GAAA,qKAAA,CAAA,OAAI,AAAD,EAAE,WAAW,OAAO;IAChC,UAAU,CAAA,GAAA,qKAAA,CAAA,OAAI,AAAD,EAAE,YAAY;QAAE,MAAM;IAAO,GAAG,KAAK;IAClD,iBAAiB,CAAA,GAAA,qKAAA,CAAA,OAAI,AAAD,EAAE,mBAAmB;QAAE,MAAM;IAAO,GAAG,KAAK;IAChE,YAAY,CAAA,GAAA,qKAAA,CAAA,OAAI,AAAD,EAAE;IACjB,WAAW,CAAA,GAAA,wKAAA,CAAA,UAAO,AAAD,EAAE,cAAc;QAAE,MAAM;IAAY,GAAG,OAAO,GAAG,UAAU,CAAC,IAAM,IAAI;IACvF,UAAU,CAAA,GAAA,qKAAA,CAAA,OAAI,AAAD,EAAE,YAAY;QAAE,MAAM;IAAO,GAAG,KAAK;AACpD;AAGO,MAAM,gBAAgB,CAAA,GAAA,2JAAA,CAAA,cAAW,AAAD,EAAE,kBAAkB;IACzD,IAAI,CAAA,GAAA,qKAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU,GAAG,UAAU,CAAC,IAAM,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IACpD,YAAY,CAAA,GAAA,qKAAA,CAAA,OAAI,AAAD,EAAE,eAAe;QAAE,MAAM;YAAC;YAAc;YAAO;YAAY;SAAS;IAAC,GAAG,OAAO;IAC9F,WAAW,CAAA,GAAA,qKAAA,CAAA,OAAI,AAAD,EAAE;IAChB,OAAO,CAAA,GAAA,qKAAA,CAAA,OAAI,AAAD,EAAE,SAAS,OAAO;IAC5B,SAAS,CAAA,GAAA,qKAAA,CAAA,OAAI,AAAD,EAAE,WAAW,OAAO;IAChC,SAAS,CAAA,GAAA,qKAAA,CAAA,OAAI,AAAD,EAAE;IACd,MAAM,CAAA,GAAA,qKAAA,CAAA,OAAI,AAAD,EAAE,QAAQ;QAAE,MAAM;IAAO,GAAG,KAAK;IAC1C,WAAW,CAAA,GAAA,qKAAA,CAAA,OAAI,AAAD,EAAE,aAAa;QAAE,MAAM;IAAO,GAAG,KAAK;IACpD,aAAa,CAAA,GAAA,wKAAA,CAAA,UAAO,AAAD,EAAE,gBAAgB;QAAE,MAAM;IAAY,GAAG,OAAO,GAAG,UAAU,CAAC,IAAM,IAAI;IAC3F,WAAW,CAAA,GAAA,wKAAA,CAAA,UAAO,AAAD,EAAE,cAAc;QAAE,MAAM;IAAY,GAAG,OAAO,GAAG,UAAU,CAAC,IAAM,IAAI;IACvF,UAAU,CAAA,GAAA,qKAAA,CAAA,OAAI,AAAD,EAAE,YAAY;QAAE,MAAM;IAAO,GAAG,KAAK;AACpD;AAGO,MAAM,YAAY,CAAA,GAAA,2JAAA,CAAA,cAAW,AAAD,EAAE,cAAc;IACjD,IAAI,CAAA,GAAA,qKAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU,GAAG,UAAU,CAAC,IAAM,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IACpD,QAAQ,CAAA,GAAA,qKAAA,CAAA,OAAI,AAAD,EAAE,WAAW,UAAU,CAAC,IAAM,MAAM,EAAE,EAAE;QAAE,UAAU;IAAU;IACzE,UAAU,CAAA,GAAA,qKAAA,CAAA,OAAI,AAAD,EAAE,aAAa,OAAO;IACnC,YAAY,CAAA,GAAA,qKAAA,CAAA,OAAI,AAAD,EAAE,cAAc;QAAE,MAAM;IAAO,GAAG,KAAK;IACtD,QAAQ,CAAA,GAAA,qKAAA,CAAA,OAAI,AAAD,EAAE,UAAU;QAAE,MAAM;IAAO,GAAG,KAAK;IAC9C,SAAS,CAAA,GAAA,wKAAA,CAAA,UAAO,AAAD,EAAE,WAAW;QAAE,MAAM;IAAU,GAAG,OAAO;IACxD,UAAU,CAAA,GAAA,wKAAA,CAAA,UAAO,AAAD,EAAE;IAClB,cAAc,CAAA,GAAA,qKAAA,CAAA,OAAI,AAAD,EAAE;IACnB,WAAW,CAAA,GAAA,wKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QAAE,MAAM;IAAY,GAAG,OAAO,GAAG,UAAU,CAAC,IAAM,IAAI;AACxF", "debugId": null}}, {"offset": {"line": 343, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/Node/my-assistant-v3/src/lib/db/index.ts"], "sourcesContent": ["import { drizzle } from 'drizzle-orm/better-sqlite3';\nimport Database from 'better-sqlite3';\nimport * as schema from './schema';\nimport { migrate } from 'drizzle-orm/better-sqlite3/migrator';\nimport path from 'path';\nimport fs from 'fs';\n\n// Ensure data directory exists\nconst dataDir = path.join(process.cwd(), 'data');\nif (!fs.existsSync(dataDir)) {\n  fs.mkdirSync(dataDir, { recursive: true });\n}\n\n// Create database connection\nconst sqlite = new Database(path.join(dataDir, 'agent.db'));\nsqlite.pragma('journal_mode = WAL');\n\nexport const db = drizzle(sqlite, { schema });\n\n// Initialize database with migrations\nexport async function initializeDatabase() {\n  try {\n    // Run migrations\n    await migrate(db, { migrationsFolder: path.join(process.cwd(), 'drizzle') });\n    console.log('Database initialized successfully');\n  } catch (error) {\n    console.error('Failed to initialize database:', error);\n    throw error;\n  }\n}\n\nexport { schema };\nexport default db;\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;AAEA,+BAA+B;AAC/B,MAAM,UAAU,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI;AACzC,IAAI,CAAC,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,UAAU;IAC3B,6FAAA,CAAA,UAAE,CAAC,SAAS,CAAC,SAAS;QAAE,WAAW;IAAK;AAC1C;AAEA,6BAA6B;AAC7B,MAAM,SAAS,IAAI,2HAAA,CAAA,UAAQ,CAAC,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,SAAS;AAC/C,OAAO,MAAM,CAAC;AAEP,MAAM,KAAK,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD,EAAE,QAAQ;IAAE,QAAA;AAAO;AAGpC,eAAe;IACpB,IAAI;QACF,iBAAiB;QACjB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,IAAI;YAAE,kBAAkB,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI;QAAW;QAC1E,QAAQ,GAAG,CAAC;IACd,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;;uCAGe", "debugId": null}}, {"offset": {"line": 503, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/Node/my-assistant-v3/src/lib/agent/deepseek.ts"], "sourcesContent": ["import axios, { AxiosInstance } from 'axios';\nimport { AgentConfig } from './types';\n\nexport interface DeepSeekMessage {\n  role: 'system' | 'user' | 'assistant';\n  content: string;\n}\n\nexport interface DeepSeekResponse {\n  id: string;\n  object: string;\n  created: number;\n  model: string;\n  choices: Array<{\n    index: number;\n    message: DeepSeekMessage;\n    finish_reason: string;\n  }>;\n  usage: {\n    prompt_tokens: number;\n    completion_tokens: number;\n    total_tokens: number;\n  };\n}\n\nexport interface EmbeddingResponse {\n  object: string;\n  data: Array<{\n    object: string;\n    index: number;\n    embedding: number[];\n  }>;\n  model: string;\n  usage: {\n    prompt_tokens: number;\n    total_tokens: number;\n  };\n}\n\nexport class DeepSeekClient {\n  private client: AxiosInstance;\n  private config: AgentConfig;\n\n  constructor(config: AgentConfig) {\n    this.config = config;\n    this.client = axios.create({\n      baseURL: config.deepseekBaseUrl || 'https://api.deepseek.com/v1',\n      headers: {\n        'Authorization': `Bearer ${config.deepseekApiKey}`,\n        'Content-Type': 'application/json',\n      },\n      timeout: config.timeout || 30000,\n    });\n  }\n\n  async chat(\n    messages: DeepSeekMessage[],\n    options: {\n      model?: string;\n      temperature?: number;\n      maxTokens?: number;\n      stream?: boolean;\n    } = {}\n  ): Promise<DeepSeekResponse> {\n    try {\n      const response = await this.client.post('/chat/completions', {\n        model: options.model || 'deepseek-reasoner',\n        messages,\n        temperature: options.temperature || 0.7,\n        max_tokens: options.maxTokens || 2000,\n        stream: options.stream || false,\n      });\n\n      return response.data;\n    } catch (error) {\n      if (axios.isAxiosError(error)) {\n        throw new Error(`DeepSeek API error: ${error.response?.data?.error?.message || error.message}`);\n      }\n      throw error;\n    }\n  }\n\n  async generateEmbedding(text: string, model: string = 'deepseek-embedding'): Promise<number[]> {\n    try {\n      const response = await this.client.post('/embeddings', {\n        model,\n        input: text,\n      });\n\n      const embeddingResponse: EmbeddingResponse = response.data;\n      return embeddingResponse.data[0].embedding;\n    } catch (error) {\n      if (axios.isAxiosError(error)) {\n        throw new Error(`DeepSeek Embedding API error: ${error.response?.data?.error?.message || error.message}`);\n      }\n      throw error;\n    }\n  }\n\n  async generatePlan(mission: string, context?: string): Promise<string> {\n    const messages: DeepSeekMessage[] = [\n      {\n        role: 'system',\n        content: `You are an expert service management AI agent with advanced planning capabilities. Your task is to create detailed, executable plans for missions.\n\nKey principles:\n1. Break down complex missions into clear, actionable tasks\n2. Consider dependencies between tasks\n3. Estimate realistic timeframes\n4. Identify required tools and resources\n5. Plan for potential risks and contingencies\n6. Ensure each task has clear success criteria\n\nReturn your response as a structured JSON plan with the following format:\n{\n  \"title\": \"Plan title\",\n  \"description\": \"Plan description\",\n  \"estimatedDuration\": 120,\n  \"tasks\": [\n    {\n      \"title\": \"Task title\",\n      \"description\": \"Task description\",\n      \"priority\": 0,\n      \"toolName\": \"tool_name\",\n      \"toolParams\": {},\n      \"dependencies\": [],\n      \"estimatedDuration\": 30\n    }\n  ],\n  \"reasoning\": \"Explanation of the planning approach\",\n  \"confidence\": 0.85\n}`\n      },\n      {\n        role: 'user',\n        content: `Mission: ${mission}${context ? `\\n\\nContext: ${context}` : ''}`\n      }\n    ];\n\n    const response = await this.chat(messages, { temperature: 0.3 });\n    return response.choices[0].message.content;\n  }\n\n  async reflect(\n    type: 'progress_assessment' | 'plan_optimization' | 'error_analysis' | 'success_analysis',\n    data: any\n  ): Promise<string> {\n    const systemPrompts = {\n      progress_assessment: 'You are analyzing the progress of a mission execution. Assess what has been completed, what remains, and any issues that need attention.',\n      plan_optimization: 'You are optimizing an execution plan based on current progress and learnings. Suggest improvements to increase efficiency and success probability.',\n      error_analysis: 'You are analyzing a failure or error that occurred during mission execution. Identify root causes and suggest corrective actions.',\n      success_analysis: 'You are analyzing a successful task or mission completion. Extract insights and best practices for future use.'\n    };\n\n    const messages: DeepSeekMessage[] = [\n      {\n        role: 'system',\n        content: `${systemPrompts[type]}\n\nReturn your analysis as a structured JSON response:\n{\n  \"insights\": [\"insight 1\", \"insight 2\"],\n  \"recommendations\": [\"recommendation 1\", \"recommendation 2\"],\n  \"confidence\": 0.8,\n  \"reasoning\": \"Detailed explanation of your analysis\"\n}`\n      },\n      {\n        role: 'user',\n        content: JSON.stringify(data, null, 2)\n      }\n    ];\n\n    const response = await this.chat(messages, { temperature: 0.4 });\n    return response.choices[0].message.content;\n  }\n\n  async selectTool(task: any, availableTools: string[]): Promise<string> {\n    const messages: DeepSeekMessage[] = [\n      {\n        role: 'system',\n        content: `You are selecting the most appropriate tool for a given task. Consider the task requirements and available tools.\n\nAvailable tools: ${availableTools.join(', ')}\n\nReturn your response as JSON:\n{\n  \"selectedTool\": \"tool_name\",\n  \"reasoning\": \"Why this tool is most appropriate\",\n  \"confidence\": 0.9\n}`\n      },\n      {\n        role: 'user',\n        content: `Task: ${JSON.stringify(task, null, 2)}`\n      }\n    ];\n\n    const response = await this.chat(messages, { temperature: 0.2 });\n    return response.choices[0].message.content;\n  }\n\n  async assessCompletion(mission: any, result: any): Promise<string> {\n    const messages: DeepSeekMessage[] = [\n      {\n        role: 'system',\n        content: `You are assessing whether a mission has been successfully completed based on the original goals and the execution results.\n\nReturn your assessment as JSON:\n{\n  \"completed\": true/false,\n  \"completionPercentage\": 0.95,\n  \"reasoning\": \"Detailed explanation\",\n  \"remainingWork\": [\"item 1\", \"item 2\"],\n  \"confidence\": 0.9\n}`\n      },\n      {\n        role: 'user',\n        content: `Mission: ${JSON.stringify(mission, null, 2)}\\n\\nResult: ${JSON.stringify(result, null, 2)}`\n      }\n    ];\n\n    const response = await this.chat(messages, { temperature: 0.3 });\n    return response.choices[0].message.content;\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAuCO,MAAM;IACH,OAAsB;IACtB,OAAoB;IAE5B,YAAY,MAAmB,CAAE;QAC/B,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,MAAM,GAAG,uIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;YACzB,SAAS,OAAO,eAAe,IAAI;YACnC,SAAS;gBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO,cAAc,EAAE;gBAClD,gBAAgB;YAClB;YACA,SAAS,OAAO,OAAO,IAAI;QAC7B;IACF;IAEA,MAAM,KACJ,QAA2B,EAC3B,UAKI,CAAC,CAAC,EACqB;QAC3B,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB;gBAC3D,OAAO,QAAQ,KAAK,IAAI;gBACxB;gBACA,aAAa,QAAQ,WAAW,IAAI;gBACpC,YAAY,QAAQ,SAAS,IAAI;gBACjC,QAAQ,QAAQ,MAAM,IAAI;YAC5B;YAEA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,IAAI,uIAAA,CAAA,UAAK,CAAC,YAAY,CAAC,QAAQ;gBAC7B,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,MAAM,QAAQ,EAAE,MAAM,OAAO,WAAW,MAAM,OAAO,EAAE;YAChG;YACA,MAAM;QACR;IACF;IAEA,MAAM,kBAAkB,IAAY,EAAE,QAAgB,oBAAoB,EAAqB;QAC7F,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe;gBACrD;gBACA,OAAO;YACT;YAEA,MAAM,oBAAuC,SAAS,IAAI;YAC1D,OAAO,kBAAkB,IAAI,CAAC,EAAE,CAAC,SAAS;QAC5C,EAAE,OAAO,OAAO;YACd,IAAI,uIAAA,CAAA,UAAK,CAAC,YAAY,CAAC,QAAQ;gBAC7B,MAAM,IAAI,MAAM,CAAC,8BAA8B,EAAE,MAAM,QAAQ,EAAE,MAAM,OAAO,WAAW,MAAM,OAAO,EAAE;YAC1G;YACA,MAAM;QACR;IACF;IAEA,MAAM,aAAa,OAAe,EAAE,OAAgB,EAAmB;QACrE,MAAM,WAA8B;YAClC;gBACE,MAAM;gBACN,SAAS,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA4BjB,CAAC;YACI;YACA;gBACE,MAAM;gBACN,SAAS,CAAC,SAAS,EAAE,UAAU,UAAU,CAAC,aAAa,EAAE,SAAS,GAAG,IAAI;YAC3E;SACD;QAED,MAAM,WAAW,MAAM,IAAI,CAAC,IAAI,CAAC,UAAU;YAAE,aAAa;QAAI;QAC9D,OAAO,SAAS,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO;IAC5C;IAEA,MAAM,QACJ,IAAyF,EACzF,IAAS,EACQ;QACjB,MAAM,gBAAgB;YACpB,qBAAqB;YACrB,mBAAmB;YACnB,gBAAgB;YAChB,kBAAkB;QACpB;QAEA,MAAM,WAA8B;YAClC;gBACE,MAAM;gBACN,SAAS,GAAG,aAAa,CAAC,KAAK,CAAC;;;;;;;;CAQvC,CAAC;YACI;YACA;gBACE,MAAM;gBACN,SAAS,KAAK,SAAS,CAAC,MAAM,MAAM;YACtC;SACD;QAED,MAAM,WAAW,MAAM,IAAI,CAAC,IAAI,CAAC,UAAU;YAAE,aAAa;QAAI;QAC9D,OAAO,SAAS,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO;IAC5C;IAEA,MAAM,WAAW,IAAS,EAAE,cAAwB,EAAmB;QACrE,MAAM,WAA8B;YAClC;gBACE,MAAM;gBACN,SAAS,CAAC;;iBAED,EAAE,eAAe,IAAI,CAAC,MAAM;;;;;;;CAO5C,CAAC;YACI;YACA;gBACE,MAAM;gBACN,SAAS,CAAC,MAAM,EAAE,KAAK,SAAS,CAAC,MAAM,MAAM,IAAI;YACnD;SACD;QAED,MAAM,WAAW,MAAM,IAAI,CAAC,IAAI,CAAC,UAAU;YAAE,aAAa;QAAI;QAC9D,OAAO,SAAS,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO;IAC5C;IAEA,MAAM,iBAAiB,OAAY,EAAE,MAAW,EAAmB;QACjE,MAAM,WAA8B;YAClC;gBACE,MAAM;gBACN,SAAS,CAAC;;;;;;;;;CASjB,CAAC;YACI;YACA;gBACE,MAAM;gBACN,SAAS,CAAC,SAAS,EAAE,KAAK,SAAS,CAAC,SAAS,MAAM,GAAG,YAAY,EAAE,KAAK,SAAS,CAAC,QAAQ,MAAM,IAAI;YACvG;SACD;QAED,MAAM,WAAW,MAAM,IAAI,CAAC,IAAI,CAAC,UAAU;YAAE,aAAa;QAAI;QAC9D,OAAO,SAAS,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO;IAC5C;AACF", "debugId": null}}, {"offset": {"line": 685, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/Node/my-assistant-v3/src/lib/agent/tools/base.ts"], "sourcesContent": ["import { Tool, ToolResult, ExecutionContext } from '../types';\n\nexport abstract class BaseTool implements Tool {\n  abstract name: string;\n  abstract description: string;\n  abstract parameters: Record<string, any>;\n\n  abstract execute(params: Record<string, any>, context?: ExecutionContext): Promise<ToolResult>;\n\n  protected createResult(success: boolean, data?: any, error?: string, metadata?: Record<string, any>): ToolResult {\n    return {\n      success,\n      data,\n      error,\n      metadata: {\n        ...metadata,\n        toolName: this.name,\n        timestamp: new Date().toISOString(),\n      },\n    };\n  }\n\n  protected validateParams(params: Record<string, any>, required: string[]): void {\n    for (const param of required) {\n      if (!(param in params) || params[param] === undefined || params[param] === null) {\n        throw new Error(`Missing required parameter: ${param}`);\n      }\n    }\n  }\n\n  protected async withRetry<T>(\n    operation: () => Promise<T>,\n    maxRetries: number = 3,\n    delay: number = 1000\n  ): Promise<T> {\n    let lastError: Error;\n\n    for (let attempt = 1; attempt <= maxRetries; attempt++) {\n      try {\n        return await operation();\n      } catch (error) {\n        lastError = error as Error;\n        \n        if (attempt === maxRetries) {\n          throw lastError;\n        }\n\n        // Exponential backoff\n        await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, attempt - 1)));\n      }\n    }\n\n    throw lastError!;\n  }\n}\n\nexport class ToolRegistry {\n  private tools: Map<string, Tool> = new Map();\n\n  register(tool: Tool): void {\n    this.tools.set(tool.name, tool);\n  }\n\n  get(name: string): Tool | undefined {\n    return this.tools.get(name);\n  }\n\n  getAll(): Tool[] {\n    return Array.from(this.tools.values());\n  }\n\n  getNames(): string[] {\n    return Array.from(this.tools.keys());\n  }\n\n  has(name: string): boolean {\n    return this.tools.has(name);\n  }\n}\n"], "names": [], "mappings": ";;;;AAEO,MAAe;IAOV,aAAa,OAAgB,EAAE,IAAU,EAAE,KAAc,EAAE,QAA8B,EAAc;QAC/G,OAAO;YACL;YACA;YACA;YACA,UAAU;gBACR,GAAG,QAAQ;gBACX,UAAU,IAAI,CAAC,IAAI;gBACnB,WAAW,IAAI,OAAO,WAAW;YACnC;QACF;IACF;IAEU,eAAe,MAA2B,EAAE,QAAkB,EAAQ;QAC9E,KAAK,MAAM,SAAS,SAAU;YAC5B,IAAI,CAAC,CAAC,SAAS,MAAM,KAAK,MAAM,CAAC,MAAM,KAAK,aAAa,MAAM,CAAC,MAAM,KAAK,MAAM;gBAC/E,MAAM,IAAI,MAAM,CAAC,4BAA4B,EAAE,OAAO;YACxD;QACF;IACF;IAEA,MAAgB,UACd,SAA2B,EAC3B,aAAqB,CAAC,EACtB,QAAgB,IAAI,EACR;QACZ,IAAI;QAEJ,IAAK,IAAI,UAAU,GAAG,WAAW,YAAY,UAAW;YACtD,IAAI;gBACF,OAAO,MAAM;YACf,EAAE,OAAO,OAAO;gBACd,YAAY;gBAEZ,IAAI,YAAY,YAAY;oBAC1B,MAAM;gBACR;gBAEA,sBAAsB;gBACtB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,QAAQ,KAAK,GAAG,CAAC,GAAG,UAAU;YACjF;QACF;QAEA,MAAM;IACR;AACF;AAEO,MAAM;IACH,QAA2B,IAAI,MAAM;IAE7C,SAAS,IAAU,EAAQ;QACzB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE;IAC5B;IAEA,IAAI,IAAY,EAAoB;QAClC,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;IACxB;IAEA,SAAiB;QACf,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM;IACrC;IAEA,WAAqB;QACnB,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI;IACnC;IAEA,IAAI,IAAY,EAAW;QACzB,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;IACxB;AACF", "debugId": null}}, {"offset": {"line": 918, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/Node/my-assistant-v3/src/lib/agent/tools/confluence.ts"], "sourcesContent": ["import { BaseTool } from './base';\nimport { ToolResult, ExecutionContext } from '../types';\nimport axios from 'axios';\nimport * as cheerio from 'cheerio';\nimport { DeepSeekClient } from '../deepseek';\nimport db, { schema } from '../../db';\n\nexport interface ConfluenceConfig {\n  baseUrl?: string;\n  username?: string;\n  apiToken?: string;\n}\n\nexport class ConfluenceLoaderTool extends BaseTool {\n  name = 'confluence_loader';\n  description = 'Load and index content from Confluence pages';\n  parameters = {\n    url: { type: 'string', required: true, description: 'Confluence page URL' },\n    includeAttachments: { type: 'boolean', required: false, description: 'Whether to include attachments' },\n  };\n\n  private deepseek: DeepSeekClient;\n  private config: ConfluenceConfig;\n\n  constructor(deepseek: DeepSeekClient, config: ConfluenceConfig = {}) {\n    super();\n    this.deepseek = deepseek;\n    this.config = config;\n  }\n\n  async execute(params: Record<string, any>, context?: ExecutionContext): Promise<ToolResult> {\n    try {\n      this.validateParams(params, ['url']);\n      \n      const { url, includeAttachments = false } = params;\n      \n      context?.logger.info(`Loading Confluence page: ${url}`);\n\n      // Extract content from the page\n      const content = await this.extractPageContent(url);\n      \n      // Generate embedding for the content\n      const embedding = await this.deepseek.generateEmbedding(content.text);\n      \n      // Store in knowledge base\n      const knowledgeEntry = await db.insert(schema.knowledgeBase).values({\n        sourceType: 'confluence',\n        sourceUrl: url,\n        title: content.title,\n        content: content.text,\n        summary: content.summary,\n        tags: content.tags,\n        embedding,\n        metadata: {\n          includeAttachments,\n          wordCount: content.text.split(' ').length,\n          extractedAt: new Date().toISOString(),\n        },\n      }).returning();\n\n      context?.logger.info(`Successfully indexed Confluence page: ${content.title}`);\n\n      return this.createResult(true, {\n        id: knowledgeEntry[0].id,\n        title: content.title,\n        wordCount: content.text.split(' ').length,\n        url,\n      });\n\n    } catch (error) {\n      context?.logger.error(`Failed to load Confluence page: ${error}`);\n      return this.createResult(false, null, (error as Error).message);\n    }\n  }\n\n  private async extractPageContent(url: string): Promise<{\n    title: string;\n    text: string;\n    summary: string;\n    tags: string[];\n  }> {\n    try {\n      // For demo purposes, we'll use web scraping\n      // In production, you'd use the Confluence REST API\n      const response = await axios.get(url, {\n        headers: {\n          'User-Agent': 'Mozilla/5.0 (compatible; ServiceAgent/1.0)',\n        },\n        timeout: 10000,\n      });\n\n      const $ = cheerio.load(response.data);\n      \n      // Extract title\n      const title = $('title').text().trim() || $('h1').first().text().trim() || 'Untitled';\n      \n      // Extract main content\n      let text = '';\n      \n      // Try common Confluence content selectors\n      const contentSelectors = [\n        '#main-content',\n        '.wiki-content',\n        '.page-content',\n        'main',\n        '.content',\n        'article',\n      ];\n\n      for (const selector of contentSelectors) {\n        const content = $(selector);\n        if (content.length > 0) {\n          // Remove script and style elements\n          content.find('script, style, nav, header, footer').remove();\n          text = content.text().trim();\n          break;\n        }\n      }\n\n      // Fallback to body content if no specific content area found\n      if (!text) {\n        $('script, style, nav, header, footer').remove();\n        text = $('body').text().trim();\n      }\n\n      // Clean up the text\n      text = text.replace(/\\s+/g, ' ').trim();\n\n      // Generate summary (first 200 words)\n      const words = text.split(' ');\n      const summary = words.slice(0, 200).join(' ') + (words.length > 200 ? '...' : '');\n\n      // Extract potential tags from headings and meta tags\n      const tags: string[] = [];\n      \n      // From headings\n      $('h1, h2, h3').each((_, el) => {\n        const heading = $(el).text().trim();\n        if (heading && heading.length < 50) {\n          tags.push(heading.toLowerCase());\n        }\n      });\n\n      // From meta keywords\n      const metaKeywords = $('meta[name=\"keywords\"]').attr('content');\n      if (metaKeywords) {\n        tags.push(...metaKeywords.split(',').map(tag => tag.trim().toLowerCase()));\n      }\n\n      // Remove duplicates and limit to 10 tags\n      const uniqueTags = [...new Set(tags)].slice(0, 10);\n\n      return {\n        title,\n        text,\n        summary,\n        tags: uniqueTags,\n      };\n\n    } catch (error) {\n      throw new Error(`Failed to extract content from ${url}: ${(error as Error).message}`);\n    }\n  }\n}\n\nexport class ConfluenceSearchTool extends BaseTool {\n  name = 'confluence_search';\n  description = 'Search indexed Confluence content using natural language';\n  parameters = {\n    query: { type: 'string', required: true, description: 'Natural language search query' },\n    limit: { type: 'number', required: false, description: 'Maximum number of results to return' },\n  };\n\n  private deepseek: DeepSeekClient;\n\n  constructor(deepseek: DeepSeekClient) {\n    super();\n    this.deepseek = deepseek;\n  }\n\n  async execute(params: Record<string, any>, context?: ExecutionContext): Promise<ToolResult> {\n    try {\n      this.validateParams(params, ['query']);\n      \n      const { query, limit = 5 } = params;\n      \n      context?.logger.info(`Searching Confluence content for: ${query}`);\n\n      // Generate embedding for the search query\n      const queryEmbedding = await this.deepseek.generateEmbedding(query);\n\n      // Search for similar content using vector similarity\n      // Note: This is a simplified implementation. In production, you'd use a proper vector database\n      const allContent = await db.select().from(schema.knowledgeBase)\n        .where(eq(schema.knowledgeBase.sourceType, 'confluence'));\n\n      // Calculate cosine similarity for each document\n      const results = allContent\n        .map(doc => ({\n          ...doc,\n          similarity: this.cosineSimilarity(queryEmbedding, doc.embedding || []),\n        }))\n        .filter(doc => doc.similarity > 0.3) // Minimum similarity threshold\n        .sort((a, b) => b.similarity - a.similarity)\n        .slice(0, limit);\n\n      context?.logger.info(`Found ${results.length} relevant documents`);\n\n      return this.createResult(true, {\n        query,\n        results: results.map(doc => ({\n          id: doc.id,\n          title: doc.title,\n          summary: doc.summary,\n          url: doc.sourceUrl,\n          similarity: doc.similarity,\n          tags: doc.tags,\n        })),\n        totalFound: results.length,\n      });\n\n    } catch (error) {\n      context?.logger.error(`Failed to search Confluence content: ${error}`);\n      return this.createResult(false, null, (error as Error).message);\n    }\n  }\n\n  private cosineSimilarity(a: number[], b: number[]): number {\n    if (a.length !== b.length) return 0;\n    \n    let dotProduct = 0;\n    let normA = 0;\n    let normB = 0;\n    \n    for (let i = 0; i < a.length; i++) {\n      dotProduct += a[i] * b[i];\n      normA += a[i] * a[i];\n      normB += b[i] * b[i];\n    }\n    \n    return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));\n  }\n}\n\n// Import eq from drizzle-orm\nimport { eq } from 'drizzle-orm';\n"], "names": [], "mappings": ";;;;AAAA;AAEA;AACA;AAAA;AAEA;AAAA;AAAA;AA+OA,6BAA6B;AAC7B;;;;;AAxOO,MAAM,6BAA6B,sIAAA,CAAA,WAAQ;IAChD,OAAO,oBAAoB;IAC3B,cAAc,+CAA+C;IAC7D,aAAa;QACX,KAAK;YAAE,MAAM;YAAU,UAAU;YAAM,aAAa;QAAsB;QAC1E,oBAAoB;YAAE,MAAM;YAAW,UAAU;YAAO,aAAa;QAAiC;IACxG,EAAE;IAEM,SAAyB;IACzB,OAAyB;IAEjC,YAAY,QAAwB,EAAE,SAA2B,CAAC,CAAC,CAAE;QACnE,KAAK;QACL,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,MAAM,GAAG;IAChB;IAEA,MAAM,QAAQ,MAA2B,EAAE,OAA0B,EAAuB;QAC1F,IAAI;YACF,IAAI,CAAC,cAAc,CAAC,QAAQ;gBAAC;aAAM;YAEnC,MAAM,EAAE,GAAG,EAAE,qBAAqB,KAAK,EAAE,GAAG;YAE5C,SAAS,OAAO,KAAK,CAAC,yBAAyB,EAAE,KAAK;YAEtD,gCAAgC;YAChC,MAAM,UAAU,MAAM,IAAI,CAAC,kBAAkB,CAAC;YAE9C,qCAAqC;YACrC,MAAM,YAAY,MAAM,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,QAAQ,IAAI;YAEpE,0BAA0B;YAC1B,MAAM,iBAAiB,MAAM,2IAAA,CAAA,UAAE,CAAC,MAAM,CAAC,8JAAA,CAAA,SAAM,CAAC,aAAa,EAAE,MAAM,CAAC;gBAClE,YAAY;gBACZ,WAAW;gBACX,OAAO,QAAQ,KAAK;gBACpB,SAAS,QAAQ,IAAI;gBACrB,SAAS,QAAQ,OAAO;gBACxB,MAAM,QAAQ,IAAI;gBAClB;gBACA,UAAU;oBACR;oBACA,WAAW,QAAQ,IAAI,CAAC,KAAK,CAAC,KAAK,MAAM;oBACzC,aAAa,IAAI,OAAO,WAAW;gBACrC;YACF,GAAG,SAAS;YAEZ,SAAS,OAAO,KAAK,CAAC,sCAAsC,EAAE,QAAQ,KAAK,EAAE;YAE7E,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM;gBAC7B,IAAI,cAAc,CAAC,EAAE,CAAC,EAAE;gBACxB,OAAO,QAAQ,KAAK;gBACpB,WAAW,QAAQ,IAAI,CAAC,KAAK,CAAC,KAAK,MAAM;gBACzC;YACF;QAEF,EAAE,OAAO,OAAO;YACd,SAAS,OAAO,MAAM,CAAC,gCAAgC,EAAE,OAAO;YAChE,OAAO,IAAI,CAAC,YAAY,CAAC,OAAO,MAAM,AAAC,MAAgB,OAAO;QAChE;IACF;IAEA,MAAc,mBAAmB,GAAW,EAKzC;QACD,IAAI;YACF,4CAA4C;YAC5C,mDAAmD;YACnD,MAAM,WAAW,MAAM,uIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,KAAK;gBACpC,SAAS;oBACP,cAAc;gBAChB;gBACA,SAAS;YACX;YAEA,MAAM,IAAI,CAAA,GAAA,yJAAA,CAAA,OAAY,AAAD,EAAE,SAAS,IAAI;YAEpC,gBAAgB;YAChB,MAAM,QAAQ,EAAE,SAAS,IAAI,GAAG,IAAI,MAAM,EAAE,MAAM,KAAK,GAAG,IAAI,GAAG,IAAI,MAAM;YAE3E,uBAAuB;YACvB,IAAI,OAAO;YAEX,0CAA0C;YAC1C,MAAM,mBAAmB;gBACvB;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YAED,KAAK,MAAM,YAAY,iBAAkB;gBACvC,MAAM,UAAU,EAAE;gBAClB,IAAI,QAAQ,MAAM,GAAG,GAAG;oBACtB,mCAAmC;oBACnC,QAAQ,IAAI,CAAC,sCAAsC,MAAM;oBACzD,OAAO,QAAQ,IAAI,GAAG,IAAI;oBAC1B;gBACF;YACF;YAEA,6DAA6D;YAC7D,IAAI,CAAC,MAAM;gBACT,EAAE,sCAAsC,MAAM;gBAC9C,OAAO,EAAE,QAAQ,IAAI,GAAG,IAAI;YAC9B;YAEA,oBAAoB;YACpB,OAAO,KAAK,OAAO,CAAC,QAAQ,KAAK,IAAI;YAErC,qCAAqC;YACrC,MAAM,QAAQ,KAAK,KAAK,CAAC;YACzB,MAAM,UAAU,MAAM,KAAK,CAAC,GAAG,KAAK,IAAI,CAAC,OAAO,CAAC,MAAM,MAAM,GAAG,MAAM,QAAQ,EAAE;YAEhF,qDAAqD;YACrD,MAAM,OAAiB,EAAE;YAEzB,gBAAgB;YAChB,EAAE,cAAc,IAAI,CAAC,CAAC,GAAG;gBACvB,MAAM,UAAU,EAAE,IAAI,IAAI,GAAG,IAAI;gBACjC,IAAI,WAAW,QAAQ,MAAM,GAAG,IAAI;oBAClC,KAAK,IAAI,CAAC,QAAQ,WAAW;gBAC/B;YACF;YAEA,qBAAqB;YACrB,MAAM,eAAe,EAAE,yBAAyB,IAAI,CAAC;YACrD,IAAI,cAAc;gBAChB,KAAK,IAAI,IAAI,aAAa,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,MAAO,IAAI,IAAI,GAAG,WAAW;YACxE;YAEA,yCAAyC;YACzC,MAAM,aAAa;mBAAI,IAAI,IAAI;aAAM,CAAC,KAAK,CAAC,GAAG;YAE/C,OAAO;gBACL;gBACA;gBACA;gBACA,MAAM;YACR;QAEF,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,MAAM,CAAC,+BAA+B,EAAE,IAAI,EAAE,EAAE,AAAC,MAAgB,OAAO,EAAE;QACtF;IACF;AACF;AAEO,MAAM,6BAA6B,sIAAA,CAAA,WAAQ;IAChD,OAAO,oBAAoB;IAC3B,cAAc,2DAA2D;IACzE,aAAa;QACX,OAAO;YAAE,MAAM;YAAU,UAAU;YAAM,aAAa;QAAgC;QACtF,OAAO;YAAE,MAAM;YAAU,UAAU;YAAO,aAAa;QAAsC;IAC/F,EAAE;IAEM,SAAyB;IAEjC,YAAY,QAAwB,CAAE;QACpC,KAAK;QACL,IAAI,CAAC,QAAQ,GAAG;IAClB;IAEA,MAAM,QAAQ,MAA2B,EAAE,OAA0B,EAAuB;QAC1F,IAAI;YACF,IAAI,CAAC,cAAc,CAAC,QAAQ;gBAAC;aAAQ;YAErC,MAAM,EAAE,KAAK,EAAE,QAAQ,CAAC,EAAE,GAAG;YAE7B,SAAS,OAAO,KAAK,CAAC,kCAAkC,EAAE,OAAO;YAEjE,0CAA0C;YAC1C,MAAM,iBAAiB,MAAM,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC;YAE7D,qDAAqD;YACrD,+FAA+F;YAC/F,MAAM,aAAa,MAAM,2IAAA,CAAA,UAAE,CAAC,MAAM,GAAG,IAAI,CAAC,8JAAA,CAAA,SAAM,CAAC,aAAa,EAC3D,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,8JAAA,CAAA,SAAM,CAAC,aAAa,CAAC,UAAU,EAAE;YAE7C,gDAAgD;YAChD,MAAM,UAAU,WACb,GAAG,CAAC,CAAA,MAAO,CAAC;oBACX,GAAG,GAAG;oBACN,YAAY,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,IAAI,SAAS,IAAI,EAAE;gBACvE,CAAC,GACA,MAAM,CAAC,CAAA,MAAO,IAAI,UAAU,GAAG,KAAK,+BAA+B;aACnE,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,UAAU,GAAG,EAAE,UAAU,EAC1C,KAAK,CAAC,GAAG;YAEZ,SAAS,OAAO,KAAK,CAAC,MAAM,EAAE,QAAQ,MAAM,CAAC,mBAAmB,CAAC;YAEjE,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM;gBAC7B;gBACA,SAAS,QAAQ,GAAG,CAAC,CAAA,MAAO,CAAC;wBAC3B,IAAI,IAAI,EAAE;wBACV,OAAO,IAAI,KAAK;wBAChB,SAAS,IAAI,OAAO;wBACpB,KAAK,IAAI,SAAS;wBAClB,YAAY,IAAI,UAAU;wBAC1B,MAAM,IAAI,IAAI;oBAChB,CAAC;gBACD,YAAY,QAAQ,MAAM;YAC5B;QAEF,EAAE,OAAO,OAAO;YACd,SAAS,OAAO,MAAM,CAAC,qCAAqC,EAAE,OAAO;YACrE,OAAO,IAAI,CAAC,YAAY,CAAC,OAAO,MAAM,AAAC,MAAgB,OAAO;QAChE;IACF;IAEQ,iBAAiB,CAAW,EAAE,CAAW,EAAU;QACzD,IAAI,EAAE,MAAM,KAAK,EAAE,MAAM,EAAE,OAAO;QAElC,IAAI,aAAa;QACjB,IAAI,QAAQ;QACZ,IAAI,QAAQ;QAEZ,IAAK,IAAI,IAAI,GAAG,IAAI,EAAE,MAAM,EAAE,IAAK;YACjC,cAAc,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;YACzB,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;YACpB,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QACtB;QAEA,OAAO,aAAa,CAAC,KAAK,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,MAAM;IAC1D;AACF", "debugId": null}}, {"offset": {"line": 1143, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/Node/my-assistant-v3/src/lib/agent/core.ts"], "sourcesContent": ["import {\n  ServiceManagementAgent,\n  PlanningCapability,\n  ToolUseCapability,\n  AutonomousCapability,\n  ReflectiveCapability,\n  GoalOrientedCapability,\n  Mission,\n  Plan,\n  Task,\n  ExecutionContext,\n  ExecutionResult,\n  PlanningResult,\n  ReflectionResult,\n  ToolResult,\n  Tool,\n  AgentConfig,\n  Logger,\n} from './types';\nimport { DeepSeekClient } from './deepseek';\nimport { ToolRegistry } from './tools/base';\nimport { ConfluenceLoaderTool, ConfluenceSearchTool } from './tools/confluence';\nimport db, { schema } from '../db';\nimport { eq, and } from 'drizzle-orm';\nimport NodeCache from 'node-cache';\n\nexport class AgentLogger implements Logger {\n  constructor(private context: { missionId?: string; planId?: string; taskId?: string }) {}\n\n  private async log(level: 'debug' | 'info' | 'warn' | 'error', message: string, data?: any) {\n    console.log(`[${level.toUpperCase()}] ${message}`, data ? JSON.stringify(data, null, 2) : '');\n    \n    // Store in database\n    try {\n      await db.insert(schema.executionLogs).values({\n        missionId: this.context.missionId,\n        planId: this.context.planId,\n        taskId: this.context.taskId,\n        level,\n        message,\n        data,\n      });\n    } catch (error) {\n      console.error('Failed to store log:', error);\n    }\n  }\n\n  debug(message: string, data?: any) { this.log('debug', message, data); }\n  info(message: string, data?: any) { this.log('info', message, data); }\n  warn(message: string, data?: any) { this.log('warn', message, data); }\n  error(message: string, data?: any) { this.log('error', message, data); }\n}\n\nexport class PlanningEngine implements PlanningCapability {\n  constructor(private deepseek: DeepSeekClient) {}\n\n  async createPlan(mission: Mission, context: ExecutionContext): Promise<PlanningResult> {\n    context.logger.info(`Creating plan for mission: ${mission.title}`);\n\n    try {\n      const planResponse = await this.deepseek.generatePlan(\n        `${mission.title}\\n\\n${mission.description}`,\n        `Priority: ${mission.priority}`\n      );\n\n      const planData = JSON.parse(planResponse);\n      \n      // Create plan in database\n      const [newPlan] = await db.insert(schema.plans).values({\n        missionId: mission.id,\n        title: planData.title,\n        description: planData.description,\n        estimatedDuration: planData.estimatedDuration,\n        status: 'draft',\n      }).returning();\n\n      // Create tasks\n      const tasks: Task[] = [];\n      for (const taskData of planData.tasks) {\n        const [newTask] = await db.insert(schema.tasks).values({\n          planId: newPlan.id,\n          title: taskData.title,\n          description: taskData.description,\n          priority: taskData.priority,\n          toolName: taskData.toolName,\n          toolParams: taskData.toolParams,\n          dependencies: taskData.dependencies,\n          estimatedDuration: taskData.estimatedDuration,\n        }).returning();\n\n        tasks.push({\n          ...newTask,\n          createdAt: new Date(newTask.createdAt),\n          updatedAt: new Date(newTask.updatedAt),\n        });\n      }\n\n      const plan: Plan = {\n        ...newPlan,\n        tasks,\n        createdAt: new Date(newPlan.createdAt),\n        updatedAt: new Date(newPlan.updatedAt),\n      };\n\n      return {\n        plan,\n        reasoning: planData.reasoning,\n        confidence: planData.confidence,\n      };\n\n    } catch (error) {\n      context.logger.error(`Failed to create plan: ${error}`);\n      throw error;\n    }\n  }\n\n  async updatePlan(plan: Plan, feedback: string, context: ExecutionContext): Promise<PlanningResult> {\n    context.logger.info(`Updating plan: ${plan.title}`);\n\n    try {\n      const updateResponse = await this.deepseek.generatePlan(\n        `Update this plan based on feedback:\\n\\nOriginal Plan: ${JSON.stringify(plan, null, 2)}\\n\\nFeedback: ${feedback}`\n      );\n\n      const updatedData = JSON.parse(updateResponse);\n      \n      // Update plan in database\n      await db.update(schema.plans)\n        .set({\n          title: updatedData.title,\n          description: updatedData.description,\n          estimatedDuration: updatedData.estimatedDuration,\n          version: plan.version + 1,\n          updatedAt: new Date(),\n        })\n        .where(eq(schema.plans.id, plan.id));\n\n      // Update tasks (simplified - in production, you'd handle this more carefully)\n      await db.delete(schema.tasks).where(eq(schema.tasks.planId, plan.id));\n      \n      const tasks: Task[] = [];\n      for (const taskData of updatedData.tasks) {\n        const [newTask] = await db.insert(schema.tasks).values({\n          planId: plan.id,\n          title: taskData.title,\n          description: taskData.description,\n          priority: taskData.priority,\n          toolName: taskData.toolName,\n          toolParams: taskData.toolParams,\n          dependencies: taskData.dependencies,\n          estimatedDuration: taskData.estimatedDuration,\n        }).returning();\n\n        tasks.push({\n          ...newTask,\n          createdAt: new Date(newTask.createdAt),\n          updatedAt: new Date(newTask.updatedAt),\n        });\n      }\n\n      const updatedPlan: Plan = {\n        ...plan,\n        title: updatedData.title,\n        description: updatedData.description,\n        estimatedDuration: updatedData.estimatedDuration,\n        version: plan.version + 1,\n        tasks,\n        updatedAt: new Date(),\n      };\n\n      return {\n        plan: updatedPlan,\n        reasoning: updatedData.reasoning,\n        confidence: updatedData.confidence,\n      };\n\n    } catch (error) {\n      context.logger.error(`Failed to update plan: ${error}`);\n      throw error;\n    }\n  }\n\n  async decomposeTasks(task: Task, context: ExecutionContext): Promise<Task[]> {\n    context.logger.info(`Decomposing task: ${task.title}`);\n\n    try {\n      const decompositionResponse = await this.deepseek.generatePlan(\n        `Decompose this task into smaller subtasks:\\n\\n${JSON.stringify(task, null, 2)}`\n      );\n\n      const decompositionData = JSON.parse(decompositionResponse);\n      \n      const subtasks: Task[] = [];\n      for (const subtaskData of decompositionData.tasks) {\n        const [newSubtask] = await db.insert(schema.tasks).values({\n          planId: task.planId,\n          parentTaskId: task.id,\n          title: subtaskData.title,\n          description: subtaskData.description,\n          priority: subtaskData.priority,\n          toolName: subtaskData.toolName,\n          toolParams: subtaskData.toolParams,\n          dependencies: subtaskData.dependencies,\n          estimatedDuration: subtaskData.estimatedDuration,\n        }).returning();\n\n        subtasks.push({\n          ...newSubtask,\n          createdAt: new Date(newSubtask.createdAt),\n          updatedAt: new Date(newSubtask.updatedAt),\n        });\n      }\n\n      return subtasks;\n\n    } catch (error) {\n      context.logger.error(`Failed to decompose task: ${error}`);\n      throw error;\n    }\n  }\n}\n\nexport class ToolUseEngine implements ToolUseCapability {\n  constructor(\n    private deepseek: DeepSeekClient,\n    private toolRegistry: ToolRegistry\n  ) {}\n\n  getAvailableTools(): Tool[] {\n    return this.toolRegistry.getAll();\n  }\n\n  async selectTool(task: Task, context: ExecutionContext): Promise<Tool | null> {\n    if (task.toolName) {\n      return this.toolRegistry.get(task.toolName) || null;\n    }\n\n    try {\n      const selectionResponse = await this.deepseek.selectTool(\n        task,\n        this.toolRegistry.getNames()\n      );\n\n      const selectionData = JSON.parse(selectionResponse);\n      return this.toolRegistry.get(selectionData.selectedTool) || null;\n\n    } catch (error) {\n      context.logger.error(`Failed to select tool: ${error}`);\n      return null;\n    }\n  }\n\n  async executeTool(tool: Tool, params: Record<string, any>, context: ExecutionContext): Promise<ToolResult> {\n    context.logger.info(`Executing tool: ${tool.name}`);\n\n    const startTime = Date.now();\n    try {\n      const result = await tool.execute(params, context);\n      const duration = Date.now() - startTime;\n\n      // Log tool usage\n      await db.insert(schema.toolUsage).values({\n        taskId: context.taskId,\n        toolName: tool.name,\n        parameters: params,\n        result: result.data,\n        success: result.success,\n        duration,\n        errorMessage: result.error,\n      });\n\n      return result;\n\n    } catch (error) {\n      const duration = Date.now() - startTime;\n\n      await db.insert(schema.toolUsage).values({\n        taskId: context.taskId,\n        toolName: tool.name,\n        parameters: params,\n        result: null,\n        success: false,\n        duration,\n        errorMessage: (error as Error).message,\n      });\n\n      throw error;\n    }\n  }\n}\n\nexport class AutonomousEngine implements AutonomousCapability {\n  constructor(\n    private toolUse: ToolUseEngine,\n    private deepseek: DeepSeekClient\n  ) {}\n\n  async executeTask(task: Task, context: ExecutionContext): Promise<ToolResult> {\n    context.logger.info(`Executing task: ${task.title}`);\n\n    try {\n      // Update task status to in_progress\n      await db.update(schema.tasks)\n        .set({ status: 'in_progress', startedAt: new Date() })\n        .where(eq(schema.tasks.id, task.id));\n\n      // Select appropriate tool\n      const tool = await this.toolUse.selectTool(task, context);\n      if (!tool) {\n        throw new Error(`No suitable tool found for task: ${task.title}`);\n      }\n\n      // Execute the tool\n      const result = await this.toolUse.executeTool(\n        tool,\n        task.toolParams || {},\n        { ...context, taskId: task.id }\n      );\n\n      // Update task status based on result\n      const status = result.success ? 'completed' : 'failed';\n      await db.update(schema.tasks)\n        .set({\n          status,\n          completedAt: new Date(),\n          result: result.data,\n        })\n        .where(eq(schema.tasks.id, task.id));\n\n      return result;\n\n    } catch (error) {\n      context.logger.error(`Task execution failed: ${error}`);\n\n      await db.update(schema.tasks)\n        .set({\n          status: 'failed',\n          completedAt: new Date(),\n          result: { error: (error as Error).message },\n        })\n        .where(eq(schema.tasks.id, task.id));\n\n      throw error;\n    }\n  }\n\n  async executePlan(plan: Plan, context: ExecutionContext): Promise<ExecutionResult> {\n    context.logger.info(`Executing plan: ${plan.title}`);\n\n    const completedTasks: Task[] = [];\n    const failedTasks: Task[] = [];\n    const reflections: any[] = [];\n\n    try {\n      // Update plan status to active\n      await db.update(schema.plans)\n        .set({ status: 'active' })\n        .where(eq(schema.plans.id, plan.id));\n\n      // Execute tasks in priority order, respecting dependencies\n      const sortedTasks = this.sortTasksByPriorityAndDependencies(plan.tasks);\n\n      for (const task of sortedTasks) {\n        try {\n          // Check if dependencies are completed\n          if (task.dependencies && task.dependencies.length > 0) {\n            const dependencyStatuses = await db.select()\n              .from(schema.tasks)\n              .where(and(\n                eq(schema.tasks.planId, plan.id),\n                // Note: In production, you'd use a proper IN clause\n              ));\n\n            const incompleteDeps = task.dependencies.filter(depId =>\n              !dependencyStatuses.find(t => t.id === depId && t.status === 'completed')\n            );\n\n            if (incompleteDeps.length > 0) {\n              context.logger.warn(`Skipping task ${task.title} due to incomplete dependencies`);\n              continue;\n            }\n          }\n\n          const result = await this.executeTask(task, context);\n          if (result.success) {\n            completedTasks.push(task);\n          } else {\n            failedTasks.push(task);\n          }\n\n        } catch (error) {\n          context.logger.error(`Task failed: ${task.title}`, error);\n          failedTasks.push(task);\n        }\n      }\n\n      // Update plan status\n      const finalStatus = failedTasks.length === 0 ? 'completed' : 'failed';\n      await db.update(schema.plans)\n        .set({ status: finalStatus })\n        .where(eq(schema.plans.id, plan.id));\n\n      return {\n        success: failedTasks.length === 0,\n        completedTasks,\n        failedTasks,\n        reflections,\n      };\n\n    } catch (error) {\n      context.logger.error(`Plan execution failed: ${error}`);\n\n      await db.update(schema.plans)\n        .set({ status: 'failed' })\n        .where(eq(schema.plans.id, plan.id));\n\n      return {\n        success: false,\n        completedTasks,\n        failedTasks,\n        reflections,\n        error: (error as Error).message,\n      };\n    }\n  }\n\n  async handleError(error: Error, context: ExecutionContext): Promise<void> {\n    context.logger.error(`Handling error: ${error.message}`);\n\n    // Store error analysis\n    await db.insert(schema.reflections).values({\n      missionId: context.missionId,\n      planId: context.planId,\n      taskId: context.taskId,\n      type: 'error_analysis',\n      content: error.message,\n      insights: [`Error occurred: ${error.message}`],\n      recommendations: ['Review task parameters', 'Check tool availability'],\n      confidence: 0.8,\n    });\n  }\n\n  private sortTasksByPriorityAndDependencies(tasks: Task[]): Task[] {\n    // Simple topological sort with priority consideration\n    const sorted: Task[] = [];\n    const visited = new Set<string>();\n    const visiting = new Set<string>();\n\n    const visit = (task: Task) => {\n      if (visiting.has(task.id)) {\n        throw new Error(`Circular dependency detected involving task: ${task.title}`);\n      }\n      if (visited.has(task.id)) return;\n\n      visiting.add(task.id);\n\n      // Visit dependencies first\n      if (task.dependencies) {\n        for (const depId of task.dependencies) {\n          const depTask = tasks.find(t => t.id === depId);\n          if (depTask) {\n            visit(depTask);\n          }\n        }\n      }\n\n      visiting.delete(task.id);\n      visited.add(task.id);\n      sorted.push(task);\n    };\n\n    // Sort by priority first, then visit\n    const prioritySorted = [...tasks].sort((a, b) => a.priority - b.priority);\n    for (const task of prioritySorted) {\n      visit(task);\n    }\n\n    return sorted;\n  }\n}\n\nexport class ReflectiveEngine implements ReflectiveCapability {\n  constructor(private deepseek: DeepSeekClient) {}\n\n  async assessProgress(plan: Plan, context: ExecutionContext): Promise<ReflectionResult> {\n    context.logger.info(`Assessing progress for plan: ${plan.title}`);\n\n    try {\n      // Get current task statuses\n      const tasks = await db.select().from(schema.tasks)\n        .where(eq(schema.tasks.planId, plan.id));\n\n      const completedTasks = tasks.filter(t => t.status === 'completed');\n      const failedTasks = tasks.filter(t => t.status === 'failed');\n      const inProgressTasks = tasks.filter(t => t.status === 'in_progress');\n\n      const progressData = {\n        plan,\n        totalTasks: tasks.length,\n        completedTasks: completedTasks.length,\n        failedTasks: failedTasks.length,\n        inProgressTasks: inProgressTasks.length,\n        progressPercentage: tasks.length > 0 ? (completedTasks.length / tasks.length) * 100 : 0,\n      };\n\n      const reflectionResponse = await this.deepseek.reflect('progress_assessment', progressData);\n      const reflectionData = JSON.parse(reflectionResponse);\n\n      // Store reflection\n      await db.insert(schema.reflections).values({\n        missionId: context.missionId,\n        planId: context.planId,\n        type: 'progress_assessment',\n        content: reflectionData.reasoning,\n        insights: reflectionData.insights,\n        recommendations: reflectionData.recommendations,\n        confidence: reflectionData.confidence,\n      });\n\n      return {\n        insights: reflectionData.insights,\n        recommendations: reflectionData.recommendations,\n        confidence: reflectionData.confidence,\n      };\n\n    } catch (error) {\n      context.logger.error(`Failed to assess progress: ${error}`);\n      throw error;\n    }\n  }\n\n  async analyzeFailure(task: Task, error: string, context: ExecutionContext): Promise<ReflectionResult> {\n    context.logger.info(`Analyzing failure for task: ${task.title}`);\n\n    try {\n      const failureData = { task, error };\n      const reflectionResponse = await this.deepseek.reflect('error_analysis', failureData);\n      const reflectionData = JSON.parse(reflectionResponse);\n\n      // Store reflection\n      await db.insert(schema.reflections).values({\n        missionId: context.missionId,\n        planId: context.planId,\n        taskId: task.id,\n        type: 'error_analysis',\n        content: reflectionData.reasoning,\n        insights: reflectionData.insights,\n        recommendations: reflectionData.recommendations,\n        confidence: reflectionData.confidence,\n      });\n\n      return {\n        insights: reflectionData.insights,\n        recommendations: reflectionData.recommendations,\n        confidence: reflectionData.confidence,\n      };\n\n    } catch (error) {\n      context.logger.error(`Failed to analyze failure: ${error}`);\n      throw error;\n    }\n  }\n\n  async optimizePlan(plan: Plan, context: ExecutionContext): Promise<ReflectionResult> {\n    context.logger.info(`Optimizing plan: ${plan.title}`);\n\n    try {\n      // Get execution history and performance data\n      const tasks = await db.select().from(schema.tasks)\n        .where(eq(schema.tasks.planId, plan.id));\n\n      const toolUsage = await db.select().from(schema.toolUsage)\n        .where(eq(schema.toolUsage.taskId, tasks[0]?.id || ''));\n\n      const optimizationData = { plan, tasks, toolUsage };\n      const reflectionResponse = await this.deepseek.reflect('plan_optimization', optimizationData);\n      const reflectionData = JSON.parse(reflectionResponse);\n\n      // Store reflection\n      await db.insert(schema.reflections).values({\n        missionId: context.missionId,\n        planId: context.planId,\n        type: 'plan_optimization',\n        content: reflectionData.reasoning,\n        insights: reflectionData.insights,\n        recommendations: reflectionData.recommendations,\n        confidence: reflectionData.confidence,\n      });\n\n      return {\n        insights: reflectionData.insights,\n        recommendations: reflectionData.recommendations,\n        confidence: reflectionData.confidence,\n      };\n\n    } catch (error) {\n      context.logger.error(`Failed to optimize plan: ${error}`);\n      throw error;\n    }\n  }\n}\n\nexport class GoalOrientedEngine implements GoalOrientedCapability {\n  constructor(private deepseek: DeepSeekClient) {}\n\n  async trackProgress(mission: Mission, context: ExecutionContext): Promise<number> {\n    context.logger.info(`Tracking progress for mission: ${mission.title}`);\n\n    try {\n      // Get all plans for this mission\n      const plans = await db.select().from(schema.plans)\n        .where(eq(schema.plans.missionId, mission.id));\n\n      if (plans.length === 0) return 0;\n\n      // Get all tasks for all plans\n      const allTasks = await db.select().from(schema.tasks)\n        .where(eq(schema.tasks.planId, plans[0].id)); // Simplified for demo\n\n      const completedTasks = allTasks.filter(t => t.status === 'completed');\n      const progress = allTasks.length > 0 ? completedTasks.length / allTasks.length : 0;\n\n      return Math.min(progress, 1.0);\n\n    } catch (error) {\n      context.logger.error(`Failed to track progress: ${error}`);\n      return 0;\n    }\n  }\n\n  async validateCompletion(mission: Mission, result: any, context: ExecutionContext): Promise<boolean> {\n    context.logger.info(`Validating completion for mission: ${mission.title}`);\n\n    try {\n      const assessmentResponse = await this.deepseek.assessCompletion(mission, result);\n      const assessmentData = JSON.parse(assessmentResponse);\n\n      return assessmentData.completed && assessmentData.completionPercentage >= 0.9;\n\n    } catch (error) {\n      context.logger.error(`Failed to validate completion: ${error}`);\n      return false;\n    }\n  }\n\n  async adjustStrategy(mission: Mission, plan: Plan, context: ExecutionContext): Promise<Plan> {\n    context.logger.info(`Adjusting strategy for mission: ${mission.title}`);\n\n    try {\n      // Get current progress and performance data\n      const progress = await this.trackProgress(mission, context);\n\n      if (progress < 0.5) {\n        // If progress is low, consider plan adjustments\n        const adjustmentData = { mission, plan, progress };\n        const adjustmentResponse = await this.deepseek.generatePlan(\n          `Adjust this plan to improve progress:\\n\\n${JSON.stringify(adjustmentData, null, 2)}`\n        );\n\n        const adjustmentPlan = JSON.parse(adjustmentResponse);\n\n        // Create new plan version\n        const [newPlan] = await db.insert(schema.plans).values({\n          missionId: mission.id,\n          version: plan.version + 1,\n          title: adjustmentPlan.title,\n          description: adjustmentPlan.description,\n          estimatedDuration: adjustmentPlan.estimatedDuration,\n          status: 'draft',\n        }).returning();\n\n        // Mark old plan as superseded\n        await db.update(schema.plans)\n          .set({ status: 'superseded' })\n          .where(eq(schema.plans.id, plan.id));\n\n        return {\n          ...newPlan,\n          tasks: [], // Tasks would be created separately\n          createdAt: new Date(newPlan.createdAt),\n          updatedAt: new Date(newPlan.updatedAt),\n        };\n      }\n\n      return plan; // No adjustment needed\n\n    } catch (error) {\n      context.logger.error(`Failed to adjust strategy: ${error}`);\n      return plan;\n    }\n  }\n}\n\nexport class ServiceManagementAgentImpl implements ServiceManagementAgent {\n  public planning: PlanningCapability;\n  public toolUse: ToolUseCapability;\n  public autonomous: AutonomousCapability;\n  public reflective: ReflectiveCapability;\n  public goalOriented: GoalOrientedCapability;\n\n  private deepseek: DeepSeekClient;\n  private toolRegistry: ToolRegistry;\n  private cache: NodeCache;\n\n  constructor(config: AgentConfig) {\n    this.deepseek = new DeepSeekClient(config);\n    this.toolRegistry = new ToolRegistry();\n    this.cache = new NodeCache({ stdTTL: 3600 }); // 1 hour cache\n\n    // Initialize capabilities\n    this.planning = new PlanningEngine(this.deepseek);\n    this.toolUse = new ToolUseEngine(this.deepseek, this.toolRegistry);\n    this.autonomous = new AutonomousEngine(this.toolUse as ToolUseEngine, this.deepseek);\n    this.reflective = new ReflectiveEngine(this.deepseek);\n    this.goalOriented = new GoalOrientedEngine(this.deepseek);\n\n    // Register default tools\n    this.registerDefaultTools();\n  }\n\n  private registerDefaultTools() {\n    this.toolRegistry.register(new ConfluenceLoaderTool(this.deepseek));\n    this.toolRegistry.register(new ConfluenceSearchTool(this.deepseek));\n  }\n\n  async executeMission(mission: Mission): Promise<ExecutionResult> {\n    const logger = new AgentLogger({ missionId: mission.id });\n    logger.info(`Starting mission execution: ${mission.title}`);\n\n    try {\n      // Update mission status\n      await db.update(schema.missions)\n        .set({ status: 'planning' })\n        .where(eq(schema.missions.id, mission.id));\n\n      // Create execution context\n      const context: ExecutionContext = {\n        missionId: mission.id,\n        planId: '',\n        tools: new Map(this.toolRegistry.getAll().map(tool => [tool.name, tool])),\n        cache: this.cache,\n        logger,\n      };\n\n      // Create initial plan\n      const planningResult = await this.planning.createPlan(mission, context);\n      context.planId = planningResult.plan.id;\n\n      // Update mission status to executing\n      await db.update(schema.missions)\n        .set({ status: 'executing' })\n        .where(eq(schema.missions.id, mission.id));\n\n      // Execute the plan\n      const executionResult = await this.autonomous.executePlan(planningResult.plan, context);\n\n      // Assess final completion\n      const isCompleted = await this.goalOriented.validateCompletion(\n        mission,\n        executionResult,\n        context\n      );\n\n      // Update mission status\n      const finalStatus = isCompleted ? 'completed' : 'failed';\n      await db.update(schema.missions)\n        .set({\n          status: finalStatus,\n          completedAt: isCompleted ? new Date() : undefined,\n        })\n        .where(eq(schema.missions.id, mission.id));\n\n      logger.info(`Mission ${isCompleted ? 'completed' : 'failed'}: ${mission.title}`);\n\n      return {\n        ...executionResult,\n        success: isCompleted,\n      };\n\n    } catch (error) {\n      logger.error(`Mission execution failed: ${error}`);\n\n      await db.update(schema.missions)\n        .set({ status: 'failed' })\n        .where(eq(schema.missions.id, mission.id));\n\n      return {\n        success: false,\n        completedTasks: [],\n        failedTasks: [],\n        reflections: [],\n        error: (error as Error).message,\n      };\n    }\n  }\n\n  async pauseMission(missionId: string): Promise<void> {\n    // Implementation for pausing mission\n    await db.update(schema.missions)\n      .set({ status: 'pending' })\n      .where(eq(schema.missions.id, missionId));\n  }\n\n  async resumeMission(missionId: string): Promise<void> {\n    // Implementation for resuming mission\n    const mission = await db.select().from(schema.missions)\n      .where(eq(schema.missions.id, missionId))\n      .limit(1);\n\n    if (mission.length > 0) {\n      await this.executeMission({\n        ...mission[0],\n        createdAt: new Date(mission[0].createdAt),\n        updatedAt: new Date(mission[0].updatedAt),\n        completedAt: mission[0].completedAt ? new Date(mission[0].completedAt) : undefined,\n      });\n    }\n  }\n\n  async cancelMission(missionId: string): Promise<void> {\n    // Implementation for canceling mission\n    await db.update(schema.missions)\n      .set({ status: 'failed' })\n      .where(eq(schema.missions.id, missionId));\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;AAmBA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;;;;;;;AAEO,MAAM;;IACX,YAAY,AAAQ,OAAiE,CAAE;aAAnE,UAAA;IAAoE;IAExF,MAAc,IAAI,KAA0C,EAAE,OAAe,EAAE,IAAU,EAAE;QACzF,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,MAAM,WAAW,GAAG,EAAE,EAAE,SAAS,EAAE,OAAO,KAAK,SAAS,CAAC,MAAM,MAAM,KAAK;QAE1F,oBAAoB;QACpB,IAAI;YACF,MAAM,2IAAA,CAAA,UAAE,CAAC,MAAM,CAAC,8JAAA,CAAA,SAAM,CAAC,aAAa,EAAE,MAAM,CAAC;gBAC3C,WAAW,IAAI,CAAC,OAAO,CAAC,SAAS;gBACjC,QAAQ,IAAI,CAAC,OAAO,CAAC,MAAM;gBAC3B,QAAQ,IAAI,CAAC,OAAO,CAAC,MAAM;gBAC3B;gBACA;gBACA;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC;IACF;IAEA,MAAM,OAAe,EAAE,IAAU,EAAE;QAAE,IAAI,CAAC,GAAG,CAAC,SAAS,SAAS;IAAO;IACvE,KAAK,OAAe,EAAE,IAAU,EAAE;QAAE,IAAI,CAAC,GAAG,CAAC,QAAQ,SAAS;IAAO;IACrE,KAAK,OAAe,EAAE,IAAU,EAAE;QAAE,IAAI,CAAC,GAAG,CAAC,QAAQ,SAAS;IAAO;IACrE,MAAM,OAAe,EAAE,IAAU,EAAE;QAAE,IAAI,CAAC,GAAG,CAAC,SAAS,SAAS;IAAO;AACzE;AAEO,MAAM;;IACX,YAAY,AAAQ,QAAwB,CAAE;aAA1B,WAAA;IAA2B;IAE/C,MAAM,WAAW,OAAgB,EAAE,OAAyB,EAA2B;QACrF,QAAQ,MAAM,CAAC,IAAI,CAAC,CAAC,2BAA2B,EAAE,QAAQ,KAAK,EAAE;QAEjE,IAAI;YACF,MAAM,eAAe,MAAM,IAAI,CAAC,QAAQ,CAAC,YAAY,CACnD,GAAG,QAAQ,KAAK,CAAC,IAAI,EAAE,QAAQ,WAAW,EAAE,EAC5C,CAAC,UAAU,EAAE,QAAQ,QAAQ,EAAE;YAGjC,MAAM,WAAW,KAAK,KAAK,CAAC;YAE5B,0BAA0B;YAC1B,MAAM,CAAC,QAAQ,GAAG,MAAM,2IAAA,CAAA,UAAE,CAAC,MAAM,CAAC,8JAAA,CAAA,SAAM,CAAC,KAAK,EAAE,MAAM,CAAC;gBACrD,WAAW,QAAQ,EAAE;gBACrB,OAAO,SAAS,KAAK;gBACrB,aAAa,SAAS,WAAW;gBACjC,mBAAmB,SAAS,iBAAiB;gBAC7C,QAAQ;YACV,GAAG,SAAS;YAEZ,eAAe;YACf,MAAM,QAAgB,EAAE;YACxB,KAAK,MAAM,YAAY,SAAS,KAAK,CAAE;gBACrC,MAAM,CAAC,QAAQ,GAAG,MAAM,2IAAA,CAAA,UAAE,CAAC,MAAM,CAAC,8JAAA,CAAA,SAAM,CAAC,KAAK,EAAE,MAAM,CAAC;oBACrD,QAAQ,QAAQ,EAAE;oBAClB,OAAO,SAAS,KAAK;oBACrB,aAAa,SAAS,WAAW;oBACjC,UAAU,SAAS,QAAQ;oBAC3B,UAAU,SAAS,QAAQ;oBAC3B,YAAY,SAAS,UAAU;oBAC/B,cAAc,SAAS,YAAY;oBACnC,mBAAmB,SAAS,iBAAiB;gBAC/C,GAAG,SAAS;gBAEZ,MAAM,IAAI,CAAC;oBACT,GAAG,OAAO;oBACV,WAAW,IAAI,KAAK,QAAQ,SAAS;oBACrC,WAAW,IAAI,KAAK,QAAQ,SAAS;gBACvC;YACF;YAEA,MAAM,OAAa;gBACjB,GAAG,OAAO;gBACV;gBACA,WAAW,IAAI,KAAK,QAAQ,SAAS;gBACrC,WAAW,IAAI,KAAK,QAAQ,SAAS;YACvC;YAEA,OAAO;gBACL;gBACA,WAAW,SAAS,SAAS;gBAC7B,YAAY,SAAS,UAAU;YACjC;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,MAAM,CAAC,KAAK,CAAC,CAAC,uBAAuB,EAAE,OAAO;YACtD,MAAM;QACR;IACF;IAEA,MAAM,WAAW,IAAU,EAAE,QAAgB,EAAE,OAAyB,EAA2B;QACjG,QAAQ,MAAM,CAAC,IAAI,CAAC,CAAC,eAAe,EAAE,KAAK,KAAK,EAAE;QAElD,IAAI;YACF,MAAM,iBAAiB,MAAM,IAAI,CAAC,QAAQ,CAAC,YAAY,CACrD,CAAC,sDAAsD,EAAE,KAAK,SAAS,CAAC,MAAM,MAAM,GAAG,cAAc,EAAE,UAAU;YAGnH,MAAM,cAAc,KAAK,KAAK,CAAC;YAE/B,0BAA0B;YAC1B,MAAM,2IAAA,CAAA,UAAE,CAAC,MAAM,CAAC,8JAAA,CAAA,SAAM,CAAC,KAAK,EACzB,GAAG,CAAC;gBACH,OAAO,YAAY,KAAK;gBACxB,aAAa,YAAY,WAAW;gBACpC,mBAAmB,YAAY,iBAAiB;gBAChD,SAAS,KAAK,OAAO,GAAG;gBACxB,WAAW,IAAI;YACjB,GACC,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,8JAAA,CAAA,SAAM,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,EAAE;YAEpC,8EAA8E;YAC9E,MAAM,2IAAA,CAAA,UAAE,CAAC,MAAM,CAAC,8JAAA,CAAA,SAAM,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,8JAAA,CAAA,SAAM,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,EAAE;YAEnE,MAAM,QAAgB,EAAE;YACxB,KAAK,MAAM,YAAY,YAAY,KAAK,CAAE;gBACxC,MAAM,CAAC,QAAQ,GAAG,MAAM,2IAAA,CAAA,UAAE,CAAC,MAAM,CAAC,8JAAA,CAAA,SAAM,CAAC,KAAK,EAAE,MAAM,CAAC;oBACrD,QAAQ,KAAK,EAAE;oBACf,OAAO,SAAS,KAAK;oBACrB,aAAa,SAAS,WAAW;oBACjC,UAAU,SAAS,QAAQ;oBAC3B,UAAU,SAAS,QAAQ;oBAC3B,YAAY,SAAS,UAAU;oBAC/B,cAAc,SAAS,YAAY;oBACnC,mBAAmB,SAAS,iBAAiB;gBAC/C,GAAG,SAAS;gBAEZ,MAAM,IAAI,CAAC;oBACT,GAAG,OAAO;oBACV,WAAW,IAAI,KAAK,QAAQ,SAAS;oBACrC,WAAW,IAAI,KAAK,QAAQ,SAAS;gBACvC;YACF;YAEA,MAAM,cAAoB;gBACxB,GAAG,IAAI;gBACP,OAAO,YAAY,KAAK;gBACxB,aAAa,YAAY,WAAW;gBACpC,mBAAmB,YAAY,iBAAiB;gBAChD,SAAS,KAAK,OAAO,GAAG;gBACxB;gBACA,WAAW,IAAI;YACjB;YAEA,OAAO;gBACL,MAAM;gBACN,WAAW,YAAY,SAAS;gBAChC,YAAY,YAAY,UAAU;YACpC;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,MAAM,CAAC,KAAK,CAAC,CAAC,uBAAuB,EAAE,OAAO;YACtD,MAAM;QACR;IACF;IAEA,MAAM,eAAe,IAAU,EAAE,OAAyB,EAAmB;QAC3E,QAAQ,MAAM,CAAC,IAAI,CAAC,CAAC,kBAAkB,EAAE,KAAK,KAAK,EAAE;QAErD,IAAI;YACF,MAAM,wBAAwB,MAAM,IAAI,CAAC,QAAQ,CAAC,YAAY,CAC5D,CAAC,8CAA8C,EAAE,KAAK,SAAS,CAAC,MAAM,MAAM,IAAI;YAGlF,MAAM,oBAAoB,KAAK,KAAK,CAAC;YAErC,MAAM,WAAmB,EAAE;YAC3B,KAAK,MAAM,eAAe,kBAAkB,KAAK,CAAE;gBACjD,MAAM,CAAC,WAAW,GAAG,MAAM,2IAAA,CAAA,UAAE,CAAC,MAAM,CAAC,8JAAA,CAAA,SAAM,CAAC,KAAK,EAAE,MAAM,CAAC;oBACxD,QAAQ,KAAK,MAAM;oBACnB,cAAc,KAAK,EAAE;oBACrB,OAAO,YAAY,KAAK;oBACxB,aAAa,YAAY,WAAW;oBACpC,UAAU,YAAY,QAAQ;oBAC9B,UAAU,YAAY,QAAQ;oBAC9B,YAAY,YAAY,UAAU;oBAClC,cAAc,YAAY,YAAY;oBACtC,mBAAmB,YAAY,iBAAiB;gBAClD,GAAG,SAAS;gBAEZ,SAAS,IAAI,CAAC;oBACZ,GAAG,UAAU;oBACb,WAAW,IAAI,KAAK,WAAW,SAAS;oBACxC,WAAW,IAAI,KAAK,WAAW,SAAS;gBAC1C;YACF;YAEA,OAAO;QAET,EAAE,OAAO,OAAO;YACd,QAAQ,MAAM,CAAC,KAAK,CAAC,CAAC,0BAA0B,EAAE,OAAO;YACzD,MAAM;QACR;IACF;AACF;AAEO,MAAM;;;IACX,YACE,AAAQ,QAAwB,EAChC,AAAQ,YAA0B,CAClC;aAFQ,WAAA;aACA,eAAA;IACP;IAEH,oBAA4B;QAC1B,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM;IACjC;IAEA,MAAM,WAAW,IAAU,EAAE,OAAyB,EAAwB;QAC5E,IAAI,KAAK,QAAQ,EAAE;YACjB,OAAO,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,QAAQ,KAAK;QACjD;QAEA,IAAI;YACF,MAAM,oBAAoB,MAAM,IAAI,CAAC,QAAQ,CAAC,UAAU,CACtD,MACA,IAAI,CAAC,YAAY,CAAC,QAAQ;YAG5B,MAAM,gBAAgB,KAAK,KAAK,CAAC;YACjC,OAAO,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,cAAc,YAAY,KAAK;QAE9D,EAAE,OAAO,OAAO;YACd,QAAQ,MAAM,CAAC,KAAK,CAAC,CAAC,uBAAuB,EAAE,OAAO;YACtD,OAAO;QACT;IACF;IAEA,MAAM,YAAY,IAAU,EAAE,MAA2B,EAAE,OAAyB,EAAuB;QACzG,QAAQ,MAAM,CAAC,IAAI,CAAC,CAAC,gBAAgB,EAAE,KAAK,IAAI,EAAE;QAElD,MAAM,YAAY,KAAK,GAAG;QAC1B,IAAI;YACF,MAAM,SAAS,MAAM,KAAK,OAAO,CAAC,QAAQ;YAC1C,MAAM,WAAW,KAAK,GAAG,KAAK;YAE9B,iBAAiB;YACjB,MAAM,2IAAA,CAAA,UAAE,CAAC,MAAM,CAAC,8JAAA,CAAA,SAAM,CAAC,SAAS,EAAE,MAAM,CAAC;gBACvC,QAAQ,QAAQ,MAAM;gBACtB,UAAU,KAAK,IAAI;gBACnB,YAAY;gBACZ,QAAQ,OAAO,IAAI;gBACnB,SAAS,OAAO,OAAO;gBACvB;gBACA,cAAc,OAAO,KAAK;YAC5B;YAEA,OAAO;QAET,EAAE,OAAO,OAAO;YACd,MAAM,WAAW,KAAK,GAAG,KAAK;YAE9B,MAAM,2IAAA,CAAA,UAAE,CAAC,MAAM,CAAC,8JAAA,CAAA,SAAM,CAAC,SAAS,EAAE,MAAM,CAAC;gBACvC,QAAQ,QAAQ,MAAM;gBACtB,UAAU,KAAK,IAAI;gBACnB,YAAY;gBACZ,QAAQ;gBACR,SAAS;gBACT;gBACA,cAAc,AAAC,MAAgB,OAAO;YACxC;YAEA,MAAM;QACR;IACF;AACF;AAEO,MAAM;;;IACX,YACE,AAAQ,OAAsB,EAC9B,AAAQ,QAAwB,CAChC;aAFQ,UAAA;aACA,WAAA;IACP;IAEH,MAAM,YAAY,IAAU,EAAE,OAAyB,EAAuB;QAC5E,QAAQ,MAAM,CAAC,IAAI,CAAC,CAAC,gBAAgB,EAAE,KAAK,KAAK,EAAE;QAEnD,IAAI;YACF,oCAAoC;YACpC,MAAM,2IAAA,CAAA,UAAE,CAAC,MAAM,CAAC,8JAAA,CAAA,SAAM,CAAC,KAAK,EACzB,GAAG,CAAC;gBAAE,QAAQ;gBAAe,WAAW,IAAI;YAAO,GACnD,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,8JAAA,CAAA,SAAM,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,EAAE;YAEpC,0BAA0B;YAC1B,MAAM,OAAO,MAAM,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,MAAM;YACjD,IAAI,CAAC,MAAM;gBACT,MAAM,IAAI,MAAM,CAAC,iCAAiC,EAAE,KAAK,KAAK,EAAE;YAClE;YAEA,mBAAmB;YACnB,MAAM,SAAS,MAAM,IAAI,CAAC,OAAO,CAAC,WAAW,CAC3C,MACA,KAAK,UAAU,IAAI,CAAC,GACpB;gBAAE,GAAG,OAAO;gBAAE,QAAQ,KAAK,EAAE;YAAC;YAGhC,qCAAqC;YACrC,MAAM,SAAS,OAAO,OAAO,GAAG,cAAc;YAC9C,MAAM,2IAAA,CAAA,UAAE,CAAC,MAAM,CAAC,8JAAA,CAAA,SAAM,CAAC,KAAK,EACzB,GAAG,CAAC;gBACH;gBACA,aAAa,IAAI;gBACjB,QAAQ,OAAO,IAAI;YACrB,GACC,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,8JAAA,CAAA,SAAM,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,EAAE;YAEpC,OAAO;QAET,EAAE,OAAO,OAAO;YACd,QAAQ,MAAM,CAAC,KAAK,CAAC,CAAC,uBAAuB,EAAE,OAAO;YAEtD,MAAM,2IAAA,CAAA,UAAE,CAAC,MAAM,CAAC,8JAAA,CAAA,SAAM,CAAC,KAAK,EACzB,GAAG,CAAC;gBACH,QAAQ;gBACR,aAAa,IAAI;gBACjB,QAAQ;oBAAE,OAAO,AAAC,MAAgB,OAAO;gBAAC;YAC5C,GACC,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,8JAAA,CAAA,SAAM,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,EAAE;YAEpC,MAAM;QACR;IACF;IAEA,MAAM,YAAY,IAAU,EAAE,OAAyB,EAA4B;QACjF,QAAQ,MAAM,CAAC,IAAI,CAAC,CAAC,gBAAgB,EAAE,KAAK,KAAK,EAAE;QAEnD,MAAM,iBAAyB,EAAE;QACjC,MAAM,cAAsB,EAAE;QAC9B,MAAM,cAAqB,EAAE;QAE7B,IAAI;YACF,+BAA+B;YAC/B,MAAM,2IAAA,CAAA,UAAE,CAAC,MAAM,CAAC,8JAAA,CAAA,SAAM,CAAC,KAAK,EACzB,GAAG,CAAC;gBAAE,QAAQ;YAAS,GACvB,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,8JAAA,CAAA,SAAM,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,EAAE;YAEpC,2DAA2D;YAC3D,MAAM,cAAc,IAAI,CAAC,kCAAkC,CAAC,KAAK,KAAK;YAEtE,KAAK,MAAM,QAAQ,YAAa;gBAC9B,IAAI;oBACF,sCAAsC;oBACtC,IAAI,KAAK,YAAY,IAAI,KAAK,YAAY,CAAC,MAAM,GAAG,GAAG;wBACrD,MAAM,qBAAqB,MAAM,2IAAA,CAAA,UAAE,CAAC,MAAM,GACvC,IAAI,CAAC,8JAAA,CAAA,SAAM,CAAC,KAAK,EACjB,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,MAAG,AAAD,EACP,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,8JAAA,CAAA,SAAM,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,EAAE;wBAInC,MAAM,iBAAiB,KAAK,YAAY,CAAC,MAAM,CAAC,CAAA,QAC9C,CAAC,mBAAmB,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,SAAS,EAAE,MAAM,KAAK;wBAG/D,IAAI,eAAe,MAAM,GAAG,GAAG;4BAC7B,QAAQ,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,EAAE,KAAK,KAAK,CAAC,+BAA+B,CAAC;4BAChF;wBACF;oBACF;oBAEA,MAAM,SAAS,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM;oBAC5C,IAAI,OAAO,OAAO,EAAE;wBAClB,eAAe,IAAI,CAAC;oBACtB,OAAO;wBACL,YAAY,IAAI,CAAC;oBACnB;gBAEF,EAAE,OAAO,OAAO;oBACd,QAAQ,MAAM,CAAC,KAAK,CAAC,CAAC,aAAa,EAAE,KAAK,KAAK,EAAE,EAAE;oBACnD,YAAY,IAAI,CAAC;gBACnB;YACF;YAEA,qBAAqB;YACrB,MAAM,cAAc,YAAY,MAAM,KAAK,IAAI,cAAc;YAC7D,MAAM,2IAAA,CAAA,UAAE,CAAC,MAAM,CAAC,8JAAA,CAAA,SAAM,CAAC,KAAK,EACzB,GAAG,CAAC;gBAAE,QAAQ;YAAY,GAC1B,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,8JAAA,CAAA,SAAM,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,EAAE;YAEpC,OAAO;gBACL,SAAS,YAAY,MAAM,KAAK;gBAChC;gBACA;gBACA;YACF;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,MAAM,CAAC,KAAK,CAAC,CAAC,uBAAuB,EAAE,OAAO;YAEtD,MAAM,2IAAA,CAAA,UAAE,CAAC,MAAM,CAAC,8JAAA,CAAA,SAAM,CAAC,KAAK,EACzB,GAAG,CAAC;gBAAE,QAAQ;YAAS,GACvB,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,8JAAA,CAAA,SAAM,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,EAAE;YAEpC,OAAO;gBACL,SAAS;gBACT;gBACA;gBACA;gBACA,OAAO,AAAC,MAAgB,OAAO;YACjC;QACF;IACF;IAEA,MAAM,YAAY,KAAY,EAAE,OAAyB,EAAiB;QACxE,QAAQ,MAAM,CAAC,KAAK,CAAC,CAAC,gBAAgB,EAAE,MAAM,OAAO,EAAE;QAEvD,uBAAuB;QACvB,MAAM,2IAAA,CAAA,UAAE,CAAC,MAAM,CAAC,8JAAA,CAAA,SAAM,CAAC,WAAW,EAAE,MAAM,CAAC;YACzC,WAAW,QAAQ,SAAS;YAC5B,QAAQ,QAAQ,MAAM;YACtB,QAAQ,QAAQ,MAAM;YACtB,MAAM;YACN,SAAS,MAAM,OAAO;YACtB,UAAU;gBAAC,CAAC,gBAAgB,EAAE,MAAM,OAAO,EAAE;aAAC;YAC9C,iBAAiB;gBAAC;gBAA0B;aAA0B;YACtE,YAAY;QACd;IACF;IAEQ,mCAAmC,KAAa,EAAU;QAChE,sDAAsD;QACtD,MAAM,SAAiB,EAAE;QACzB,MAAM,UAAU,IAAI;QACpB,MAAM,WAAW,IAAI;QAErB,MAAM,QAAQ,CAAC;YACb,IAAI,SAAS,GAAG,CAAC,KAAK,EAAE,GAAG;gBACzB,MAAM,IAAI,MAAM,CAAC,6CAA6C,EAAE,KAAK,KAAK,EAAE;YAC9E;YACA,IAAI,QAAQ,GAAG,CAAC,KAAK,EAAE,GAAG;YAE1B,SAAS,GAAG,CAAC,KAAK,EAAE;YAEpB,2BAA2B;YAC3B,IAAI,KAAK,YAAY,EAAE;gBACrB,KAAK,MAAM,SAAS,KAAK,YAAY,CAAE;oBACrC,MAAM,UAAU,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;oBACzC,IAAI,SAAS;wBACX,MAAM;oBACR;gBACF;YACF;YAEA,SAAS,MAAM,CAAC,KAAK,EAAE;YACvB,QAAQ,GAAG,CAAC,KAAK,EAAE;YACnB,OAAO,IAAI,CAAC;QACd;QAEA,qCAAqC;QACrC,MAAM,iBAAiB;eAAI;SAAM,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ;QACxE,KAAK,MAAM,QAAQ,eAAgB;YACjC,MAAM;QACR;QAEA,OAAO;IACT;AACF;AAEO,MAAM;;IACX,YAAY,AAAQ,QAAwB,CAAE;aAA1B,WAAA;IAA2B;IAE/C,MAAM,eAAe,IAAU,EAAE,OAAyB,EAA6B;QACrF,QAAQ,MAAM,CAAC,IAAI,CAAC,CAAC,6BAA6B,EAAE,KAAK,KAAK,EAAE;QAEhE,IAAI;YACF,4BAA4B;YAC5B,MAAM,QAAQ,MAAM,2IAAA,CAAA,UAAE,CAAC,MAAM,GAAG,IAAI,CAAC,8JAAA,CAAA,SAAM,CAAC,KAAK,EAC9C,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,8JAAA,CAAA,SAAM,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,EAAE;YAExC,MAAM,iBAAiB,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;YACtD,MAAM,cAAc,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;YACnD,MAAM,kBAAkB,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;YAEvD,MAAM,eAAe;gBACnB;gBACA,YAAY,MAAM,MAAM;gBACxB,gBAAgB,eAAe,MAAM;gBACrC,aAAa,YAAY,MAAM;gBAC/B,iBAAiB,gBAAgB,MAAM;gBACvC,oBAAoB,MAAM,MAAM,GAAG,IAAI,AAAC,eAAe,MAAM,GAAG,MAAM,MAAM,GAAI,MAAM;YACxF;YAEA,MAAM,qBAAqB,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,uBAAuB;YAC9E,MAAM,iBAAiB,KAAK,KAAK,CAAC;YAElC,mBAAmB;YACnB,MAAM,2IAAA,CAAA,UAAE,CAAC,MAAM,CAAC,8JAAA,CAAA,SAAM,CAAC,WAAW,EAAE,MAAM,CAAC;gBACzC,WAAW,QAAQ,SAAS;gBAC5B,QAAQ,QAAQ,MAAM;gBACtB,MAAM;gBACN,SAAS,eAAe,SAAS;gBACjC,UAAU,eAAe,QAAQ;gBACjC,iBAAiB,eAAe,eAAe;gBAC/C,YAAY,eAAe,UAAU;YACvC;YAEA,OAAO;gBACL,UAAU,eAAe,QAAQ;gBACjC,iBAAiB,eAAe,eAAe;gBAC/C,YAAY,eAAe,UAAU;YACvC;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,MAAM,CAAC,KAAK,CAAC,CAAC,2BAA2B,EAAE,OAAO;YAC1D,MAAM;QACR;IACF;IAEA,MAAM,eAAe,IAAU,EAAE,KAAa,EAAE,OAAyB,EAA6B;QACpG,QAAQ,MAAM,CAAC,IAAI,CAAC,CAAC,4BAA4B,EAAE,KAAK,KAAK,EAAE;QAE/D,IAAI;YACF,MAAM,cAAc;gBAAE;gBAAM;YAAM;YAClC,MAAM,qBAAqB,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,kBAAkB;YACzE,MAAM,iBAAiB,KAAK,KAAK,CAAC;YAElC,mBAAmB;YACnB,MAAM,2IAAA,CAAA,UAAE,CAAC,MAAM,CAAC,8JAAA,CAAA,SAAM,CAAC,WAAW,EAAE,MAAM,CAAC;gBACzC,WAAW,QAAQ,SAAS;gBAC5B,QAAQ,QAAQ,MAAM;gBACtB,QAAQ,KAAK,EAAE;gBACf,MAAM;gBACN,SAAS,eAAe,SAAS;gBACjC,UAAU,eAAe,QAAQ;gBACjC,iBAAiB,eAAe,eAAe;gBAC/C,YAAY,eAAe,UAAU;YACvC;YAEA,OAAO;gBACL,UAAU,eAAe,QAAQ;gBACjC,iBAAiB,eAAe,eAAe;gBAC/C,YAAY,eAAe,UAAU;YACvC;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,MAAM,CAAC,KAAK,CAAC,CAAC,2BAA2B,EAAE,OAAO;YAC1D,MAAM;QACR;IACF;IAEA,MAAM,aAAa,IAAU,EAAE,OAAyB,EAA6B;QACnF,QAAQ,MAAM,CAAC,IAAI,CAAC,CAAC,iBAAiB,EAAE,KAAK,KAAK,EAAE;QAEpD,IAAI;YACF,6CAA6C;YAC7C,MAAM,QAAQ,MAAM,2IAAA,CAAA,UAAE,CAAC,MAAM,GAAG,IAAI,CAAC,8JAAA,CAAA,SAAM,CAAC,KAAK,EAC9C,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,8JAAA,CAAA,SAAM,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,EAAE;YAExC,MAAM,YAAY,MAAM,2IAAA,CAAA,UAAE,CAAC,MAAM,GAAG,IAAI,CAAC,8JAAA,CAAA,SAAM,CAAC,SAAS,EACtD,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,8JAAA,CAAA,SAAM,CAAC,SAAS,CAAC,MAAM,EAAE,KAAK,CAAC,EAAE,EAAE,MAAM;YAErD,MAAM,mBAAmB;gBAAE;gBAAM;gBAAO;YAAU;YAClD,MAAM,qBAAqB,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,qBAAqB;YAC5E,MAAM,iBAAiB,KAAK,KAAK,CAAC;YAElC,mBAAmB;YACnB,MAAM,2IAAA,CAAA,UAAE,CAAC,MAAM,CAAC,8JAAA,CAAA,SAAM,CAAC,WAAW,EAAE,MAAM,CAAC;gBACzC,WAAW,QAAQ,SAAS;gBAC5B,QAAQ,QAAQ,MAAM;gBACtB,MAAM;gBACN,SAAS,eAAe,SAAS;gBACjC,UAAU,eAAe,QAAQ;gBACjC,iBAAiB,eAAe,eAAe;gBAC/C,YAAY,eAAe,UAAU;YACvC;YAEA,OAAO;gBACL,UAAU,eAAe,QAAQ;gBACjC,iBAAiB,eAAe,eAAe;gBAC/C,YAAY,eAAe,UAAU;YACvC;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,MAAM,CAAC,KAAK,CAAC,CAAC,yBAAyB,EAAE,OAAO;YACxD,MAAM;QACR;IACF;AACF;AAEO,MAAM;;IACX,YAAY,AAAQ,QAAwB,CAAE;aAA1B,WAAA;IAA2B;IAE/C,MAAM,cAAc,OAAgB,EAAE,OAAyB,EAAmB;QAChF,QAAQ,MAAM,CAAC,IAAI,CAAC,CAAC,+BAA+B,EAAE,QAAQ,KAAK,EAAE;QAErE,IAAI;YACF,iCAAiC;YACjC,MAAM,QAAQ,MAAM,2IAAA,CAAA,UAAE,CAAC,MAAM,GAAG,IAAI,CAAC,8JAAA,CAAA,SAAM,CAAC,KAAK,EAC9C,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,8JAAA,CAAA,SAAM,CAAC,KAAK,CAAC,SAAS,EAAE,QAAQ,EAAE;YAE9C,IAAI,MAAM,MAAM,KAAK,GAAG,OAAO;YAE/B,8BAA8B;YAC9B,MAAM,WAAW,MAAM,2IAAA,CAAA,UAAE,CAAC,MAAM,GAAG,IAAI,CAAC,8JAAA,CAAA,SAAM,CAAC,KAAK,EACjD,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,8JAAA,CAAA,SAAM,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,EAAE,CAAC,EAAE,IAAI,sBAAsB;YAEtE,MAAM,iBAAiB,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;YACzD,MAAM,WAAW,SAAS,MAAM,GAAG,IAAI,eAAe,MAAM,GAAG,SAAS,MAAM,GAAG;YAEjF,OAAO,KAAK,GAAG,CAAC,UAAU;QAE5B,EAAE,OAAO,OAAO;YACd,QAAQ,MAAM,CAAC,KAAK,CAAC,CAAC,0BAA0B,EAAE,OAAO;YACzD,OAAO;QACT;IACF;IAEA,MAAM,mBAAmB,OAAgB,EAAE,MAAW,EAAE,OAAyB,EAAoB;QACnG,QAAQ,MAAM,CAAC,IAAI,CAAC,CAAC,mCAAmC,EAAE,QAAQ,KAAK,EAAE;QAEzE,IAAI;YACF,MAAM,qBAAqB,MAAM,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,SAAS;YACzE,MAAM,iBAAiB,KAAK,KAAK,CAAC;YAElC,OAAO,eAAe,SAAS,IAAI,eAAe,oBAAoB,IAAI;QAE5E,EAAE,OAAO,OAAO;YACd,QAAQ,MAAM,CAAC,KAAK,CAAC,CAAC,+BAA+B,EAAE,OAAO;YAC9D,OAAO;QACT;IACF;IAEA,MAAM,eAAe,OAAgB,EAAE,IAAU,EAAE,OAAyB,EAAiB;QAC3F,QAAQ,MAAM,CAAC,IAAI,CAAC,CAAC,gCAAgC,EAAE,QAAQ,KAAK,EAAE;QAEtE,IAAI;YACF,4CAA4C;YAC5C,MAAM,WAAW,MAAM,IAAI,CAAC,aAAa,CAAC,SAAS;YAEnD,IAAI,WAAW,KAAK;gBAClB,gDAAgD;gBAChD,MAAM,iBAAiB;oBAAE;oBAAS;oBAAM;gBAAS;gBACjD,MAAM,qBAAqB,MAAM,IAAI,CAAC,QAAQ,CAAC,YAAY,CACzD,CAAC,yCAAyC,EAAE,KAAK,SAAS,CAAC,gBAAgB,MAAM,IAAI;gBAGvF,MAAM,iBAAiB,KAAK,KAAK,CAAC;gBAElC,0BAA0B;gBAC1B,MAAM,CAAC,QAAQ,GAAG,MAAM,2IAAA,CAAA,UAAE,CAAC,MAAM,CAAC,8JAAA,CAAA,SAAM,CAAC,KAAK,EAAE,MAAM,CAAC;oBACrD,WAAW,QAAQ,EAAE;oBACrB,SAAS,KAAK,OAAO,GAAG;oBACxB,OAAO,eAAe,KAAK;oBAC3B,aAAa,eAAe,WAAW;oBACvC,mBAAmB,eAAe,iBAAiB;oBACnD,QAAQ;gBACV,GAAG,SAAS;gBAEZ,8BAA8B;gBAC9B,MAAM,2IAAA,CAAA,UAAE,CAAC,MAAM,CAAC,8JAAA,CAAA,SAAM,CAAC,KAAK,EACzB,GAAG,CAAC;oBAAE,QAAQ;gBAAa,GAC3B,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,8JAAA,CAAA,SAAM,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,EAAE;gBAEpC,OAAO;oBACL,GAAG,OAAO;oBACV,OAAO,EAAE;oBACT,WAAW,IAAI,KAAK,QAAQ,SAAS;oBACrC,WAAW,IAAI,KAAK,QAAQ,SAAS;gBACvC;YACF;YAEA,OAAO,MAAM,uBAAuB;QAEtC,EAAE,OAAO,OAAO;YACd,QAAQ,MAAM,CAAC,KAAK,CAAC,CAAC,2BAA2B,EAAE,OAAO;YAC1D,OAAO;QACT;IACF;AACF;AAEO,MAAM;IACJ,SAA6B;IAC7B,QAA2B;IAC3B,WAAiC;IACjC,WAAiC;IACjC,aAAqC;IAEpC,SAAyB;IACzB,aAA2B;IAC3B,MAAiB;IAEzB,YAAY,MAAmB,CAAE;QAC/B,IAAI,CAAC,QAAQ,GAAG,IAAI,iIAAA,CAAA,iBAAc,CAAC;QACnC,IAAI,CAAC,YAAY,GAAG,IAAI,sIAAA,CAAA,eAAY;QACpC,IAAI,CAAC,KAAK,GAAG,IAAI,wIAAA,CAAA,UAAS,CAAC;YAAE,QAAQ;QAAK,IAAI,eAAe;QAE7D,0BAA0B;QAC1B,IAAI,CAAC,QAAQ,GAAG,IAAI,eAAe,IAAI,CAAC,QAAQ;QAChD,IAAI,CAAC,OAAO,GAAG,IAAI,cAAc,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,YAAY;QACjE,IAAI,CAAC,UAAU,GAAG,IAAI,iBAAiB,IAAI,CAAC,OAAO,EAAmB,IAAI,CAAC,QAAQ;QACnF,IAAI,CAAC,UAAU,GAAG,IAAI,iBAAiB,IAAI,CAAC,QAAQ;QACpD,IAAI,CAAC,YAAY,GAAG,IAAI,mBAAmB,IAAI,CAAC,QAAQ;QAExD,yBAAyB;QACzB,IAAI,CAAC,oBAAoB;IAC3B;IAEQ,uBAAuB;QAC7B,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,4IAAA,CAAA,uBAAoB,CAAC,IAAI,CAAC,QAAQ;QACjE,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,4IAAA,CAAA,uBAAoB,CAAC,IAAI,CAAC,QAAQ;IACnE;IAEA,MAAM,eAAe,OAAgB,EAA4B;QAC/D,MAAM,SAAS,IAAI,YAAY;YAAE,WAAW,QAAQ,EAAE;QAAC;QACvD,OAAO,IAAI,CAAC,CAAC,4BAA4B,EAAE,QAAQ,KAAK,EAAE;QAE1D,IAAI;YACF,wBAAwB;YACxB,MAAM,2IAAA,CAAA,UAAE,CAAC,MAAM,CAAC,8JAAA,CAAA,SAAM,CAAC,QAAQ,EAC5B,GAAG,CAAC;gBAAE,QAAQ;YAAW,GACzB,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,8JAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,EAAE;YAE1C,2BAA2B;YAC3B,MAAM,UAA4B;gBAChC,WAAW,QAAQ,EAAE;gBACrB,QAAQ;gBACR,OAAO,IAAI,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,GAAG,CAAC,CAAA,OAAQ;wBAAC,KAAK,IAAI;wBAAE;qBAAK;gBACvE,OAAO,IAAI,CAAC,KAAK;gBACjB;YACF;YAEA,sBAAsB;YACtB,MAAM,iBAAiB,MAAM,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,SAAS;YAC/D,QAAQ,MAAM,GAAG,eAAe,IAAI,CAAC,EAAE;YAEvC,qCAAqC;YACrC,MAAM,2IAAA,CAAA,UAAE,CAAC,MAAM,CAAC,8JAAA,CAAA,SAAM,CAAC,QAAQ,EAC5B,GAAG,CAAC;gBAAE,QAAQ;YAAY,GAC1B,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,8JAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,EAAE;YAE1C,mBAAmB;YACnB,MAAM,kBAAkB,MAAM,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,eAAe,IAAI,EAAE;YAE/E,0BAA0B;YAC1B,MAAM,cAAc,MAAM,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAC5D,SACA,iBACA;YAGF,wBAAwB;YACxB,MAAM,cAAc,cAAc,cAAc;YAChD,MAAM,2IAAA,CAAA,UAAE,CAAC,MAAM,CAAC,8JAAA,CAAA,SAAM,CAAC,QAAQ,EAC5B,GAAG,CAAC;gBACH,QAAQ;gBACR,aAAa,cAAc,IAAI,SAAS;YAC1C,GACC,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,8JAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,EAAE;YAE1C,OAAO,IAAI,CAAC,CAAC,QAAQ,EAAE,cAAc,cAAc,SAAS,EAAE,EAAE,QAAQ,KAAK,EAAE;YAE/E,OAAO;gBACL,GAAG,eAAe;gBAClB,SAAS;YACX;QAEF,EAAE,OAAO,OAAO;YACd,OAAO,KAAK,CAAC,CAAC,0BAA0B,EAAE,OAAO;YAEjD,MAAM,2IAAA,CAAA,UAAE,CAAC,MAAM,CAAC,8JAAA,CAAA,SAAM,CAAC,QAAQ,EAC5B,GAAG,CAAC;gBAAE,QAAQ;YAAS,GACvB,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,8JAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,EAAE;YAE1C,OAAO;gBACL,SAAS;gBACT,gBAAgB,EAAE;gBAClB,aAAa,EAAE;gBACf,aAAa,EAAE;gBACf,OAAO,AAAC,MAAgB,OAAO;YACjC;QACF;IACF;IAEA,MAAM,aAAa,SAAiB,EAAiB;QACnD,qCAAqC;QACrC,MAAM,2IAAA,CAAA,UAAE,CAAC,MAAM,CAAC,8JAAA,CAAA,SAAM,CAAC,QAAQ,EAC5B,GAAG,CAAC;YAAE,QAAQ;QAAU,GACxB,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,8JAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,EAAE,EAAE;IAClC;IAEA,MAAM,cAAc,SAAiB,EAAiB;QACpD,sCAAsC;QACtC,MAAM,UAAU,MAAM,2IAAA,CAAA,UAAE,CAAC,MAAM,GAAG,IAAI,CAAC,8JAAA,CAAA,SAAM,CAAC,QAAQ,EACnD,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,8JAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,EAAE,EAAE,YAC7B,KAAK,CAAC;QAET,IAAI,QAAQ,MAAM,GAAG,GAAG;YACtB,MAAM,IAAI,CAAC,cAAc,CAAC;gBACxB,GAAG,OAAO,CAAC,EAAE;gBACb,WAAW,IAAI,KAAK,OAAO,CAAC,EAAE,CAAC,SAAS;gBACxC,WAAW,IAAI,KAAK,OAAO,CAAC,EAAE,CAAC,SAAS;gBACxC,aAAa,OAAO,CAAC,EAAE,CAAC,WAAW,GAAG,IAAI,KAAK,OAAO,CAAC,EAAE,CAAC,WAAW,IAAI;YAC3E;QACF;IACF;IAEA,MAAM,cAAc,SAAiB,EAAiB;QACpD,uCAAuC;QACvC,MAAM,2IAAA,CAAA,UAAE,CAAC,MAAM,CAAC,8JAAA,CAAA,SAAM,CAAC,QAAQ,EAC5B,GAAG,CAAC;YAAE,QAAQ;QAAS,GACvB,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,8JAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,EAAE,EAAE;IAClC;AACF", "debugId": null}}, {"offset": {"line": 1844, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/Node/my-assistant-v3/src/app/api/missions/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport db, { schema } from '@/lib/db';\nimport { ServiceManagementAgentImpl } from '@/lib/agent/core';\nimport { eq } from 'drizzle-orm';\n\n// Initialize agent (in production, this would be a singleton)\nconst agent = new ServiceManagementAgentImpl({\n  deepseekApiKey: process.env.DEEPSEEK_API_KEY || '',\n  debug: process.env.NODE_ENV === 'development',\n});\n\nexport async function GET() {\n  try {\n    const missions = await db.select().from(schema.missions)\n      .orderBy(schema.missions.createdAt);\n\n    return NextResponse.json({\n      success: true,\n      data: missions.map(mission => ({\n        ...mission,\n        createdAt: mission.createdAt.toISOString(),\n        updatedAt: mission.updatedAt.toISOString(),\n        completedAt: mission.completedAt?.toISOString(),\n      })),\n    });\n  } catch (error) {\n    console.error('Failed to fetch missions:', error);\n    return NextResponse.json(\n      { success: false, error: 'Failed to fetch missions' },\n      { status: 500 }\n    );\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json();\n    const { title, description, priority = 'medium' } = body;\n\n    if (!title || !description) {\n      return NextResponse.json(\n        { success: false, error: 'Title and description are required' },\n        { status: 400 }\n      );\n    }\n\n    // Create mission in database\n    const [mission] = await db.insert(schema.missions).values({\n      title,\n      description,\n      priority,\n      status: 'pending',\n    }).returning();\n\n    // Start mission execution asynchronously\n    const missionData = {\n      ...mission,\n      createdAt: new Date(mission.createdAt),\n      updatedAt: new Date(mission.updatedAt),\n      completedAt: mission.completedAt ? new Date(mission.completedAt) : undefined,\n    };\n\n    // Execute mission in background\n    agent.executeMission(missionData).catch(error => {\n      console.error(`Mission execution failed for ${mission.id}:`, error);\n    });\n\n    return NextResponse.json({\n      success: true,\n      data: {\n        ...mission,\n        createdAt: mission.createdAt.toISOString(),\n        updatedAt: mission.updatedAt.toISOString(),\n        completedAt: mission.completedAt?.toISOString(),\n      },\n    });\n  } catch (error) {\n    console.error('Failed to create mission:', error);\n    return NextResponse.json(\n      { success: false, error: 'Failed to create mission' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AACA;;;;AAGA,8DAA8D;AAC9D,MAAM,QAAQ,IAAI,6HAAA,CAAA,6BAA0B,CAAC;IAC3C,gBAAgB,QAAQ,GAAG,CAAC,gBAAgB,IAAI;IAChD,OAAO,oDAAyB;AAClC;AAEO,eAAe;IACpB,IAAI;QACF,MAAM,WAAW,MAAM,2IAAA,CAAA,UAAE,CAAC,MAAM,GAAG,IAAI,CAAC,8JAAA,CAAA,SAAM,CAAC,QAAQ,EACpD,OAAO,CAAC,8JAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,SAAS;QAEpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM,SAAS,GAAG,CAAC,CAAA,UAAW,CAAC;oBAC7B,GAAG,OAAO;oBACV,WAAW,QAAQ,SAAS,CAAC,WAAW;oBACxC,WAAW,QAAQ,SAAS,CAAC,WAAW;oBACxC,aAAa,QAAQ,WAAW,EAAE;gBACpC,CAAC;QACH;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAA2B,GACpD;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,WAAW,QAAQ,EAAE,GAAG;QAEpD,IAAI,CAAC,SAAS,CAAC,aAAa;YAC1B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAqC,GAC9D;gBAAE,QAAQ;YAAI;QAElB;QAEA,6BAA6B;QAC7B,MAAM,CAAC,QAAQ,GAAG,MAAM,2IAAA,CAAA,UAAE,CAAC,MAAM,CAAC,8JAAA,CAAA,SAAM,CAAC,QAAQ,EAAE,MAAM,CAAC;YACxD;YACA;YACA;YACA,QAAQ;QACV,GAAG,SAAS;QAEZ,yCAAyC;QACzC,MAAM,cAAc;YAClB,GAAG,OAAO;YACV,WAAW,IAAI,KAAK,QAAQ,SAAS;YACrC,WAAW,IAAI,KAAK,QAAQ,SAAS;YACrC,aAAa,QAAQ,WAAW,GAAG,IAAI,KAAK,QAAQ,WAAW,IAAI;QACrE;QAEA,gCAAgC;QAChC,MAAM,cAAc,CAAC,aAAa,KAAK,CAAC,CAAA;YACtC,QAAQ,KAAK,CAAC,CAAC,6BAA6B,EAAE,QAAQ,EAAE,CAAC,CAAC,CAAC,EAAE;QAC/D;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBACJ,GAAG,OAAO;gBACV,WAAW,QAAQ,SAAS,CAAC,WAAW;gBACxC,WAAW,QAAQ,SAAS,CAAC,WAAW;gBACxC,aAAa,QAAQ,WAAW,EAAE;YACpC;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAA2B,GACpD;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}