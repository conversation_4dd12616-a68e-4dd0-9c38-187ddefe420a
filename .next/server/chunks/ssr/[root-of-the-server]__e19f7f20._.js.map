{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/Node/my-assistant-v3/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/Node/my-assistant-v3/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 128, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/Node/my-assistant-v3/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 174, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/Node/my-assistant-v3/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 231, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/Node/my-assistant-v3/src/components/ui/progress.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Progress({\n  className,\n  value,\n  ...props\n}: React.ComponentProps<typeof ProgressPrimitive.Root>) {\n  return (\n    <ProgressPrimitive.Root\n      data-slot=\"progress\"\n      className={cn(\n        \"bg-primary/20 relative h-2 w-full overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    >\n      <ProgressPrimitive.Indicator\n        data-slot=\"progress-indicator\"\n        className=\"bg-primary h-full w-full flex-1 transition-all\"\n        style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n      />\n    </ProgressPrimitive.Root>\n  )\n}\n\nexport { Progress }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,SAAS,EAChB,SAAS,EACT,KAAK,EACL,GAAG,OACiD;IACpD,qBACE,8OAAC,oKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAIlE", "debugId": null}}, {"offset": {"line": 270, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/Node/my-assistant-v3/src/components/MissionDashboard.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Badge } from '@/components/ui/badge';\nimport { Button } from '@/components/ui/button';\nimport { Progress } from '@/components/ui/progress';\nimport { Separator } from '@/components/ui/separator';\nimport { \n  Target, \n  Play, \n  Pause, \n  Square, \n  CheckCircle, \n  XCircle, \n  Clock, \n  Brain,\n  Zap,\n  MessageSquare\n} from 'lucide-react';\n\ninterface Mission {\n  id: string;\n  title: string;\n  description: string;\n  status: 'pending' | 'planning' | 'executing' | 'completed' | 'failed';\n  priority: 'low' | 'medium' | 'high' | 'urgent';\n  createdAt: string;\n  updatedAt: string;\n  completedAt?: string;\n}\n\ninterface Plan {\n  id: string;\n  title: string;\n  description: string;\n  status: string;\n  estimatedDuration: number;\n  version: number;\n  createdAt: string;\n  updatedAt: string;\n}\n\ninterface Task {\n  id: string;\n  title: string;\n  description: string;\n  status: 'pending' | 'in_progress' | 'completed' | 'failed' | 'skipped';\n  priority: number;\n  toolName?: string;\n  estimatedDuration?: number;\n  actualDuration?: number;\n  startedAt?: string;\n  completedAt?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\ninterface ExecutionLog {\n  id: string;\n  level: 'debug' | 'info' | 'warn' | 'error';\n  message: string;\n  timestamp: string;\n  data?: any;\n}\n\ninterface Reflection {\n  id: string;\n  type: 'progress_assessment' | 'plan_optimization' | 'error_analysis' | 'success_analysis';\n  content: string;\n  insights?: string[];\n  recommendations?: string[];\n  confidence?: number;\n  createdAt: string;\n}\n\ninterface MissionDetails {\n  mission: Mission;\n  plans: Plan[];\n  tasks: Task[];\n  logs: ExecutionLog[];\n  reflections: Reflection[];\n}\n\ninterface MissionDashboardProps {\n  mission: Mission;\n}\n\nexport function MissionDashboard({ mission }: MissionDashboardProps) {\n  const [details, setDetails] = useState<MissionDetails | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    fetchMissionDetails();\n    \n    // Poll for updates every 5 seconds if mission is active\n    const interval = setInterval(() => {\n      if (mission.status === 'executing' || mission.status === 'planning') {\n        fetchMissionDetails();\n      }\n    }, 5000);\n\n    return () => clearInterval(interval);\n  }, [mission.id]);\n\n  const fetchMissionDetails = async () => {\n    try {\n      const response = await fetch(`/api/missions/${mission.id}`);\n      const result = await response.json();\n      if (result.success) {\n        setDetails(result.data);\n      }\n    } catch (error) {\n      console.error('Failed to fetch mission details:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleMissionAction = async (action: 'pause' | 'resume' | 'cancel') => {\n    try {\n      const response = await fetch(`/api/missions/${mission.id}`, {\n        method: 'PATCH',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ action }),\n      });\n\n      if (response.ok) {\n        await fetchMissionDetails();\n      }\n    } catch (error) {\n      console.error(`Failed to ${action} mission:`, error);\n    }\n  };\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'completed': return <CheckCircle className=\"h-4 w-4 text-green-500\" />;\n      case 'failed': return <XCircle className=\"h-4 w-4 text-red-500\" />;\n      case 'executing': return <Zap className=\"h-4 w-4 text-blue-500\" />;\n      case 'planning': return <Brain className=\"h-4 w-4 text-yellow-500\" />;\n      default: return <Clock className=\"h-4 w-4 text-gray-500\" />;\n    }\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'completed': return 'bg-green-100 text-green-800';\n      case 'failed': return 'bg-red-100 text-red-800';\n      case 'executing': return 'bg-blue-100 text-blue-800';\n      case 'planning': return 'bg-yellow-100 text-yellow-800';\n      default: return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const calculateProgress = () => {\n    if (!details?.tasks.length) return 0;\n    const completedTasks = details.tasks.filter(t => t.status === 'completed').length;\n    return (completedTasks / details.tasks.length) * 100;\n  };\n\n  if (loading) {\n    return (\n      <Card>\n        <CardContent className=\"p-6\">\n          <div className=\"text-center\">\n            <Target className=\"h-8 w-8 mx-auto mb-2 animate-pulse\" />\n            <p>Loading mission details...</p>\n          </div>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  if (!details) {\n    return (\n      <Card>\n        <CardContent className=\"p-6\">\n          <div className=\"text-center\">\n            <XCircle className=\"h-8 w-8 mx-auto mb-2 text-red-500\" />\n            <p>Failed to load mission details</p>\n          </div>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  const progress = calculateProgress();\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Mission Overview */}\n      <Card>\n        <CardHeader>\n          <div className=\"flex items-start justify-between\">\n            <div className=\"flex items-start space-x-3\">\n              {getStatusIcon(details.mission.status)}\n              <div>\n                <CardTitle>{details.mission.title}</CardTitle>\n                <CardDescription className=\"mt-1\">\n                  {details.mission.description}\n                </CardDescription>\n              </div>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <Badge className={getStatusColor(details.mission.status)}>\n                {details.mission.status}\n              </Badge>\n              {details.mission.status === 'executing' && (\n                <Button\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={() => handleMissionAction('pause')}\n                >\n                  <Pause className=\"h-4 w-4 mr-1\" />\n                  Pause\n                </Button>\n              )}\n              {details.mission.status === 'pending' && (\n                <Button\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={() => handleMissionAction('resume')}\n                >\n                  <Play className=\"h-4 w-4 mr-1\" />\n                  Resume\n                </Button>\n              )}\n              {(details.mission.status === 'executing' || details.mission.status === 'pending') && (\n                <Button\n                  variant=\"destructive\"\n                  size=\"sm\"\n                  onClick={() => handleMissionAction('cancel')}\n                >\n                  <Square className=\"h-4 w-4 mr-1\" />\n                  Cancel\n                </Button>\n              )}\n            </div>\n          </div>\n        </CardHeader>\n        <CardContent>\n          <div className=\"space-y-4\">\n            <div>\n              <div className=\"flex justify-between text-sm mb-2\">\n                <span>Progress</span>\n                <span>{Math.round(progress)}%</span>\n              </div>\n              <Progress value={progress} className=\"h-2\" />\n            </div>\n            \n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm\">\n              <div>\n                <p className=\"text-gray-500\">Total Tasks</p>\n                <p className=\"font-semibold\">{details.tasks.length}</p>\n              </div>\n              <div>\n                <p className=\"text-gray-500\">Completed</p>\n                <p className=\"font-semibold text-green-600\">\n                  {details.tasks.filter(t => t.status === 'completed').length}\n                </p>\n              </div>\n              <div>\n                <p className=\"text-gray-500\">In Progress</p>\n                <p className=\"font-semibold text-blue-600\">\n                  {details.tasks.filter(t => t.status === 'in_progress').length}\n                </p>\n              </div>\n              <div>\n                <p className=\"text-gray-500\">Failed</p>\n                <p className=\"font-semibold text-red-600\">\n                  {details.tasks.filter(t => t.status === 'failed').length}\n                </p>\n              </div>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Tasks */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"text-lg\">Tasks</CardTitle>\n            <CardDescription>Current execution plan</CardDescription>\n          </CardHeader>\n          <CardContent>\n            {details.tasks.length === 0 ? (\n              <p className=\"text-gray-500 text-center py-4\">No tasks yet</p>\n            ) : (\n              <div className=\"space-y-3 max-h-80 overflow-y-auto\">\n                {details.tasks\n                  .sort((a, b) => a.priority - b.priority)\n                  .map((task) => (\n                    <div key={task.id} className=\"p-3 border rounded-lg\">\n                      <div className=\"flex items-start justify-between\">\n                        <div className=\"flex-1\">\n                          <div className=\"flex items-center space-x-2\">\n                            {getStatusIcon(task.status)}\n                            <h4 className=\"font-medium text-sm\">{task.title}</h4>\n                          </div>\n                          {task.description && (\n                            <p className=\"text-xs text-gray-600 mt-1\">{task.description}</p>\n                          )}\n                          <div className=\"flex items-center space-x-2 mt-2\">\n                            <Badge variant=\"outline\" className=\"text-xs\">\n                              Priority {task.priority}\n                            </Badge>\n                            {task.toolName && (\n                              <Badge variant=\"secondary\" className=\"text-xs\">\n                                {task.toolName}\n                              </Badge>\n                            )}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n              </div>\n            )}\n          </CardContent>\n        </Card>\n\n        {/* Execution Logs */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"text-lg\">Execution Logs</CardTitle>\n            <CardDescription>Real-time execution updates</CardDescription>\n          </CardHeader>\n          <CardContent>\n            {details.logs.length === 0 ? (\n              <p className=\"text-gray-500 text-center py-4\">No logs yet</p>\n            ) : (\n              <div className=\"space-y-2 max-h-80 overflow-y-auto\">\n                {details.logs\n                  .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())\n                  .slice(0, 20)\n                  .map((log) => (\n                    <div key={log.id} className=\"text-xs p-2 border rounded\">\n                      <div className=\"flex items-start justify-between\">\n                        <div className=\"flex-1\">\n                          <div className=\"flex items-center space-x-2\">\n                            <Badge\n                              variant={log.level === 'error' ? 'destructive' : 'secondary'}\n                              className=\"text-xs\"\n                            >\n                              {log.level}\n                            </Badge>\n                            <span className=\"text-gray-500\">\n                              {new Date(log.timestamp).toLocaleTimeString()}\n                            </span>\n                          </div>\n                          <p className=\"mt-1\">{log.message}</p>\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n              </div>\n            )}\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Reflections */}\n      {details.reflections.length > 0 && (\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center space-x-2\">\n              <MessageSquare className=\"h-5 w-5\" />\n              <span>AI Reflections</span>\n            </CardTitle>\n            <CardDescription>Agent insights and recommendations</CardDescription>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-4\">\n              {details.reflections\n                .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())\n                .slice(0, 5)\n                .map((reflection) => (\n                  <div key={reflection.id} className=\"p-4 border rounded-lg\">\n                    <div className=\"flex items-start justify-between mb-2\">\n                      <Badge variant=\"outline\" className=\"text-xs\">\n                        {reflection.type.replace('_', ' ')}\n                      </Badge>\n                      <span className=\"text-xs text-gray-500\">\n                        {new Date(reflection.createdAt).toLocaleString()}\n                      </span>\n                    </div>\n                    <p className=\"text-sm mb-2\">{reflection.content}</p>\n                    {reflection.insights && reflection.insights.length > 0 && (\n                      <div className=\"mb-2\">\n                        <h5 className=\"text-xs font-medium text-gray-700 mb-1\">Insights:</h5>\n                        <ul className=\"text-xs text-gray-600 space-y-1\">\n                          {reflection.insights.map((insight, idx) => (\n                            <li key={idx}>• {insight}</li>\n                          ))}\n                        </ul>\n                      </div>\n                    )}\n                    {reflection.recommendations && reflection.recommendations.length > 0 && (\n                      <div>\n                        <h5 className=\"text-xs font-medium text-gray-700 mb-1\">Recommendations:</h5>\n                        <ul className=\"text-xs text-gray-600 space-y-1\">\n                          {reflection.recommendations.map((rec, idx) => (\n                            <li key={idx}>• {rec}</li>\n                          ))}\n                        </ul>\n                      </div>\n                    )}\n                  </div>\n                ))}\n            </div>\n          </CardContent>\n        </Card>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AARA;;;;;;;;AAwFO,SAAS,iBAAiB,EAAE,OAAO,EAAyB;IACjE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB;IAC9D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;QAEA,wDAAwD;QACxD,MAAM,WAAW,YAAY;YAC3B,IAAI,QAAQ,MAAM,KAAK,eAAe,QAAQ,MAAM,KAAK,YAAY;gBACnE;YACF;QACF,GAAG;QAEH,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC,QAAQ,EAAE;KAAC;IAEf,MAAM,sBAAsB;QAC1B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,QAAQ,EAAE,EAAE;YAC1D,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,IAAI,OAAO,OAAO,EAAE;gBAClB,WAAW,OAAO,IAAI;YACxB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;QACpD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,sBAAsB,OAAO;QACjC,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,QAAQ,EAAE,EAAE,EAAE;gBAC1D,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAO;YAChC;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,UAAU,EAAE,OAAO,SAAS,CAAC,EAAE;QAChD;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAa,qBAAO,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChD,KAAK;gBAAU,qBAAO,8OAAC,4MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YACzC,KAAK;gBAAa,qBAAO,8OAAC,gMAAA,CAAA,MAAG;oBAAC,WAAU;;;;;;YACxC,KAAK;gBAAY,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YACzC;gBAAS,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;QACnC;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAY,OAAO;YACxB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,SAAS,MAAM,QAAQ,OAAO;QACnC,MAAM,iBAAiB,QAAQ,KAAK,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;QACjF,OAAO,AAAC,iBAAiB,QAAQ,KAAK,CAAC,MAAM,GAAI;IACnD;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC,gIAAA,CAAA,OAAI;sBACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,sMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,8OAAC;sCAAE;;;;;;;;;;;;;;;;;;;;;;IAKb;IAEA,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC,gIAAA,CAAA,OAAI;sBACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4MAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;sCACnB,8OAAC;sCAAE;;;;;;;;;;;;;;;;;;;;;;IAKb;IAEA,MAAM,WAAW;IAEjB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;wCACZ,cAAc,QAAQ,OAAO,CAAC,MAAM;sDACrC,8OAAC;;8DACC,8OAAC,gIAAA,CAAA,YAAS;8DAAE,QAAQ,OAAO,CAAC,KAAK;;;;;;8DACjC,8OAAC,gIAAA,CAAA,kBAAe;oDAAC,WAAU;8DACxB,QAAQ,OAAO,CAAC,WAAW;;;;;;;;;;;;;;;;;;8CAIlC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,WAAW,eAAe,QAAQ,OAAO,CAAC,MAAM;sDACpD,QAAQ,OAAO,CAAC,MAAM;;;;;;wCAExB,QAAQ,OAAO,CAAC,MAAM,KAAK,6BAC1B,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,oBAAoB;;8DAEnC,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;wCAIrC,QAAQ,OAAO,CAAC,MAAM,KAAK,2BAC1B,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,oBAAoB;;8DAEnC,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;wCAIpC,CAAC,QAAQ,OAAO,CAAC,MAAM,KAAK,eAAe,QAAQ,OAAO,CAAC,MAAM,KAAK,SAAS,mBAC9E,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,oBAAoB;;8DAEnC,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;kCAO7C,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAK;;;;;;8DACN,8OAAC;;wDAAM,KAAK,KAAK,CAAC;wDAAU;;;;;;;;;;;;;sDAE9B,8OAAC,oIAAA,CAAA,WAAQ;4CAAC,OAAO;4CAAU,WAAU;;;;;;;;;;;;8CAGvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAgB;;;;;;8DAC7B,8OAAC;oDAAE,WAAU;8DAAiB,QAAQ,KAAK,CAAC,MAAM;;;;;;;;;;;;sDAEpD,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAgB;;;;;;8DAC7B,8OAAC;oDAAE,WAAU;8DACV,QAAQ,KAAK,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;;;;;;;;;;;;sDAG/D,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAgB;;;;;;8DAC7B,8OAAC;oDAAE,WAAU;8DACV,QAAQ,KAAK,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,eAAe,MAAM;;;;;;;;;;;;sDAGjE,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAgB;;;;;;8DAC7B,8OAAC;oDAAE,WAAU;8DACV,QAAQ,KAAK,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,UAAU,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQpE,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;;kDACT,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAU;;;;;;kDAC/B,8OAAC,gIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAEnB,8OAAC,gIAAA,CAAA,cAAW;0CACT,QAAQ,KAAK,CAAC,MAAM,KAAK,kBACxB,8OAAC;oCAAE,WAAU;8CAAiC;;;;;yDAE9C,8OAAC;oCAAI,WAAU;8CACZ,QAAQ,KAAK,CACX,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ,EACtC,GAAG,CAAC,CAAC,qBACJ,8OAAC;4CAAkB,WAAU;sDAC3B,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;gEACZ,cAAc,KAAK,MAAM;8EAC1B,8OAAC;oEAAG,WAAU;8EAAuB,KAAK,KAAK;;;;;;;;;;;;wDAEhD,KAAK,WAAW,kBACf,8OAAC;4DAAE,WAAU;sEAA8B,KAAK,WAAW;;;;;;sEAE7D,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,iIAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAU,WAAU;;wEAAU;wEACjC,KAAK,QAAQ;;;;;;;gEAExB,KAAK,QAAQ,kBACZ,8OAAC,iIAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAY,WAAU;8EAClC,KAAK,QAAQ;;;;;;;;;;;;;;;;;;;;;;;2CAhBhB,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;kCA8B7B,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;;kDACT,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAU;;;;;;kDAC/B,8OAAC,gIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAEnB,8OAAC,gIAAA,CAAA,cAAW;0CACT,QAAQ,IAAI,CAAC,MAAM,KAAK,kBACvB,8OAAC;oCAAE,WAAU;8CAAiC;;;;;yDAE9C,8OAAC;oCAAI,WAAU;8CACZ,QAAQ,IAAI,CACV,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,IAC9E,KAAK,CAAC,GAAG,IACT,GAAG,CAAC,CAAC,oBACJ,8OAAC;4CAAiB,WAAU;sDAC1B,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,iIAAA,CAAA,QAAK;oEACJ,SAAS,IAAI,KAAK,KAAK,UAAU,gBAAgB;oEACjD,WAAU;8EAET,IAAI,KAAK;;;;;;8EAEZ,8OAAC;oEAAK,WAAU;8EACb,IAAI,KAAK,IAAI,SAAS,EAAE,kBAAkB;;;;;;;;;;;;sEAG/C,8OAAC;4DAAE,WAAU;sEAAQ,IAAI,OAAO;;;;;;;;;;;;;;;;;2CAd5B,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;YA0B7B,QAAQ,WAAW,CAAC,MAAM,GAAG,mBAC5B,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;;0CACT,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,8OAAC,wNAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;kDACzB,8OAAC;kDAAK;;;;;;;;;;;;0CAER,8OAAC,gIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAEnB,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;sCACZ,QAAQ,WAAW,CACjB,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,IAC9E,KAAK,CAAC,GAAG,GACT,GAAG,CAAC,CAAC,2BACJ,8OAAC;oCAAwB,WAAU;;sDACjC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAU,WAAU;8DAChC,WAAW,IAAI,CAAC,OAAO,CAAC,KAAK;;;;;;8DAEhC,8OAAC;oDAAK,WAAU;8DACb,IAAI,KAAK,WAAW,SAAS,EAAE,cAAc;;;;;;;;;;;;sDAGlD,8OAAC;4CAAE,WAAU;sDAAgB,WAAW,OAAO;;;;;;wCAC9C,WAAW,QAAQ,IAAI,WAAW,QAAQ,CAAC,MAAM,GAAG,mBACnD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAyC;;;;;;8DACvD,8OAAC;oDAAG,WAAU;8DACX,WAAW,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,oBACjC,8OAAC;;gEAAa;gEAAG;;2DAAR;;;;;;;;;;;;;;;;wCAKhB,WAAW,eAAe,IAAI,WAAW,eAAe,CAAC,MAAM,GAAG,mBACjE,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAyC;;;;;;8DACvD,8OAAC;oDAAG,WAAU;8DACX,WAAW,eAAe,CAAC,GAAG,CAAC,CAAC,KAAK,oBACpC,8OAAC;;gEAAa;gEAAG;;2DAAR;;;;;;;;;;;;;;;;;mCAzBT,WAAW,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsCzC", "debugId": null}}, {"offset": {"line": 1207, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/Node/my-assistant-v3/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1233, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/Node/my-assistant-v3/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1258, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/Node/my-assistant-v3/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Separator({\n  className,\n  orientation = \"horizontal\",\n  decorative = true,\n  ...props\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\n  return (\n    <SeparatorPrimitive.Root\n      data-slot=\"separator\"\n      decorative={decorative}\n      orientation={orientation}\n      className={cn(\n        \"bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Separator }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,YAAY,EAC1B,aAAa,IAAI,EACjB,GAAG,OACkD;IACrD,qBACE,8OAAC,qKAAA,CAAA,OAAuB;QACtB,aAAU;QACV,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1288, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/Node/my-assistant-v3/src/components/ConfluencePanel.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Badge } from '@/components/ui/badge';\nimport { Separator } from '@/components/ui/separator';\nimport { FileText, Search, ExternalLink, Plus, Loader2 } from 'lucide-react';\n\ninterface ConfluenceContent {\n  id: string;\n  title: string;\n  summary: string;\n  sourceUrl: string;\n  tags: string[];\n  lastIndexed: string;\n}\n\ninterface SearchResult {\n  id: string;\n  title: string;\n  summary: string;\n  url: string;\n  similarity: number;\n  tags: string[];\n}\n\nexport function ConfluencePanel() {\n  const [confluenceUrl, setConfluenceUrl] = useState('');\n  const [searchQuery, setSearchQuery] = useState('');\n  const [indexedContent, setIndexedContent] = useState<ConfluenceContent[]>([]);\n  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [searching, setSearching] = useState(false);\n\n  useEffect(() => {\n    fetchIndexedContent();\n  }, []);\n\n  const fetchIndexedContent = async () => {\n    try {\n      const response = await fetch('/api/confluence');\n      const result = await response.json();\n      if (result.success) {\n        setIndexedContent(result.data);\n      }\n    } catch (error) {\n      console.error('Failed to fetch indexed content:', error);\n    }\n  };\n\n  const handleLoadConfluence = async () => {\n    if (!confluenceUrl.trim()) return;\n\n    setLoading(true);\n    try {\n      const response = await fetch('/api/confluence', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({\n          action: 'load',\n          url: confluenceUrl.trim(),\n        }),\n      });\n\n      const result = await response.json();\n      if (result.success) {\n        setConfluenceUrl('');\n        await fetchIndexedContent();\n      } else {\n        alert('Failed to load Confluence page: ' + result.error);\n      }\n    } catch (error) {\n      console.error('Failed to load Confluence page:', error);\n      alert('Failed to load Confluence page');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSearch = async () => {\n    if (!searchQuery.trim()) return;\n\n    setSearching(true);\n    try {\n      const response = await fetch('/api/confluence', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({\n          action: 'search',\n          query: searchQuery.trim(),\n          limit: 5,\n        }),\n      });\n\n      const result = await response.json();\n      if (result.success) {\n        setSearchResults(result.data.results || []);\n      } else {\n        alert('Search failed: ' + result.error);\n      }\n    } catch (error) {\n      console.error('Search failed:', error);\n      alert('Search failed');\n    } finally {\n      setSearching(false);\n    }\n  };\n\n  return (\n    <Card>\n      <CardHeader>\n        <CardTitle className=\"flex items-center space-x-2\">\n          <FileText className=\"h-5 w-5\" />\n          <span>Confluence Integration</span>\n        </CardTitle>\n        <CardDescription>\n          Load and search Confluence content for your missions\n        </CardDescription>\n      </CardHeader>\n      <CardContent className=\"space-y-6\">\n        {/* Load Confluence Page */}\n        <div>\n          <h3 className=\"text-sm font-medium text-gray-900 mb-2\">Load Confluence Page</h3>\n          <div className=\"space-y-2\">\n            <Input\n              value={confluenceUrl}\n              onChange={(e) => setConfluenceUrl(e.target.value)}\n              placeholder=\"https://your-company.atlassian.net/wiki/spaces/...\"\n            />\n            <Button\n              onClick={handleLoadConfluence}\n              disabled={!confluenceUrl.trim() || loading}\n              className=\"w-full\"\n              size=\"sm\"\n            >\n              {loading ? (\n                <>\n                  <Loader2 className=\"h-4 w-4 mr-2 animate-spin\" />\n                  Loading...\n                </>\n              ) : (\n                <>\n                  <Plus className=\"h-4 w-4 mr-2\" />\n                  Load & Index\n                </>\n              )}\n            </Button>\n          </div>\n        </div>\n\n        <Separator />\n\n        {/* Search */}\n        <div>\n          <h3 className=\"text-sm font-medium text-gray-900 mb-2\">Search Content</h3>\n          <div className=\"space-y-2\">\n            <Textarea\n              value={searchQuery}\n              onChange={(e) => setSearchQuery(e.target.value)}\n              placeholder=\"Ask a question or describe what you're looking for...\"\n              rows={3}\n            />\n            <Button\n              onClick={handleSearch}\n              disabled={!searchQuery.trim() || searching}\n              className=\"w-full\"\n              size=\"sm\"\n            >\n              {searching ? (\n                <>\n                  <Loader2 className=\"h-4 w-4 mr-2 animate-spin\" />\n                  Searching...\n                </>\n              ) : (\n                <>\n                  <Search className=\"h-4 w-4 mr-2\" />\n                  Search\n                </>\n              )}\n            </Button>\n          </div>\n        </div>\n\n        {/* Search Results */}\n        {searchResults.length > 0 && (\n          <div>\n            <h3 className=\"text-sm font-medium text-gray-900 mb-2\">Search Results</h3>\n            <div className=\"space-y-3\">\n              {searchResults.map((result) => (\n                <div key={result.id} className=\"p-3 border rounded-lg\">\n                  <div className=\"flex items-start justify-between\">\n                    <div className=\"flex-1\">\n                      <h4 className=\"font-medium text-sm text-gray-900\">{result.title}</h4>\n                      <p className=\"text-xs text-gray-600 mt-1\">{result.summary}</p>\n                      <div className=\"flex items-center space-x-2 mt-2\">\n                        <Badge variant=\"secondary\" className=\"text-xs\">\n                          {Math.round(result.similarity * 100)}% match\n                        </Badge>\n                        {result.tags.slice(0, 2).map((tag) => (\n                          <Badge key={tag} variant=\"outline\" className=\"text-xs\">\n                            {tag}\n                          </Badge>\n                        ))}\n                      </div>\n                    </div>\n                    <Button\n                      variant=\"ghost\"\n                      size=\"sm\"\n                      onClick={() => window.open(result.url, '_blank')}\n                      className=\"h-6 w-6 p-0\"\n                    >\n                      <ExternalLink className=\"h-3 w-3\" />\n                    </Button>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        )}\n\n        <Separator />\n\n        {/* Indexed Content */}\n        <div>\n          <h3 className=\"text-sm font-medium text-gray-900 mb-2\">\n            Indexed Content ({indexedContent.length})\n          </h3>\n          {indexedContent.length === 0 ? (\n            <p className=\"text-xs text-gray-500\">No content indexed yet</p>\n          ) : (\n            <div className=\"space-y-2 max-h-40 overflow-y-auto\">\n              {indexedContent.map((content) => (\n                <div key={content.id} className=\"p-2 border rounded text-xs\">\n                  <div className=\"flex items-start justify-between\">\n                    <div className=\"flex-1\">\n                      <h4 className=\"font-medium text-gray-900\">{content.title}</h4>\n                      <p className=\"text-gray-600 mt-1\">{content.summary}</p>\n                      <div className=\"flex flex-wrap gap-1 mt-1\">\n                        {content.tags.slice(0, 3).map((tag) => (\n                          <Badge key={tag} variant=\"outline\" className=\"text-xs\">\n                            {tag}\n                          </Badge>\n                        ))}\n                      </div>\n                    </div>\n                    <Button\n                      variant=\"ghost\"\n                      size=\"sm\"\n                      onClick={() => window.open(content.sourceUrl, '_blank')}\n                      className=\"h-5 w-5 p-0\"\n                    >\n                      <ExternalLink className=\"h-3 w-3\" />\n                    </Button>\n                  </div>\n                </div>\n              ))}\n            </div>\n          )}\n        </div>\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AATA;;;;;;;;;;AA6BO,SAAS;IACd,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB,EAAE;IAC5E,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,sBAAsB;QAC1B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,IAAI,OAAO,OAAO,EAAE;gBAClB,kBAAkB,OAAO,IAAI;YAC/B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;QACpD;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI,CAAC,cAAc,IAAI,IAAI;QAE3B,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,mBAAmB;gBAC9C,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB,QAAQ;oBACR,KAAK,cAAc,IAAI;gBACzB;YACF;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,IAAI,OAAO,OAAO,EAAE;gBAClB,iBAAiB;gBACjB,MAAM;YACR,OAAO;gBACL,MAAM,qCAAqC,OAAO,KAAK;YACzD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,YAAY,IAAI,IAAI;QAEzB,aAAa;QACb,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,mBAAmB;gBAC9C,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB,QAAQ;oBACR,OAAO,YAAY,IAAI;oBACvB,OAAO;gBACT;YACF;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,IAAI,OAAO,OAAO,EAAE;gBAClB,iBAAiB,OAAO,IAAI,CAAC,OAAO,IAAI,EAAE;YAC5C,OAAO;gBACL,MAAM,oBAAoB,OAAO,KAAK;YACxC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;YAChC,MAAM;QACR,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;;0BACH,8OAAC,gIAAA,CAAA,aAAU;;kCACT,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,8OAAC,8MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,8OAAC;0CAAK;;;;;;;;;;;;kCAER,8OAAC,gIAAA,CAAA,kBAAe;kCAAC;;;;;;;;;;;;0BAInB,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCACJ,OAAO;wCACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;wCAChD,aAAY;;;;;;kDAEd,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS;wCACT,UAAU,CAAC,cAAc,IAAI,MAAM;wCACnC,WAAU;wCACV,MAAK;kDAEJ,wBACC;;8DACE,8OAAC,iNAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;gDAA8B;;yEAInD;;8DACE,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;kCAQ3C,8OAAC,qIAAA,CAAA,YAAS;;;;;kCAGV,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oIAAA,CAAA,WAAQ;wCACP,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,aAAY;wCACZ,MAAM;;;;;;kDAER,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS;wCACT,UAAU,CAAC,YAAY,IAAI,MAAM;wCACjC,WAAU;wCACV,MAAK;kDAEJ,0BACC;;8DACE,8OAAC,iNAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;gDAA8B;;yEAInD;;8DACE,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;oBAS5C,cAAc,MAAM,GAAG,mBACtB,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC;gCAAI,WAAU;0CACZ,cAAc,GAAG,CAAC,CAAC,uBAClB,8OAAC;wCAAoB,WAAU;kDAC7B,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAAqC,OAAO,KAAK;;;;;;sEAC/D,8OAAC;4DAAE,WAAU;sEAA8B,OAAO,OAAO;;;;;;sEACzD,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,iIAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAY,WAAU;;wEAClC,KAAK,KAAK,CAAC,OAAO,UAAU,GAAG;wEAAK;;;;;;;gEAEtC,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,oBAC5B,8OAAC,iIAAA,CAAA,QAAK;wEAAW,SAAQ;wEAAU,WAAU;kFAC1C;uEADS;;;;;;;;;;;;;;;;;8DAMlB,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS,IAAM,OAAO,IAAI,CAAC,OAAO,GAAG,EAAE;oDACvC,WAAU;8DAEV,cAAA,8OAAC,sNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;;;;;;;;;;;;uCAtBpB,OAAO,EAAE;;;;;;;;;;;;;;;;kCA+B3B,8OAAC,qIAAA,CAAA,YAAS;;;;;kCAGV,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;;oCAAyC;oCACnC,eAAe,MAAM;oCAAC;;;;;;;4BAEzC,eAAe,MAAM,KAAK,kBACzB,8OAAC;gCAAE,WAAU;0CAAwB;;;;;qDAErC,8OAAC;gCAAI,WAAU;0CACZ,eAAe,GAAG,CAAC,CAAC,wBACnB,8OAAC;wCAAqB,WAAU;kDAC9B,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAA6B,QAAQ,KAAK;;;;;;sEACxD,8OAAC;4DAAE,WAAU;sEAAsB,QAAQ,OAAO;;;;;;sEAClD,8OAAC;4DAAI,WAAU;sEACZ,QAAQ,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,oBAC7B,8OAAC,iIAAA,CAAA,QAAK;oEAAW,SAAQ;oEAAU,WAAU;8EAC1C;mEADS;;;;;;;;;;;;;;;;8DAMlB,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS,IAAM,OAAO,IAAI,CAAC,QAAQ,SAAS,EAAE;oDAC9C,WAAU;8DAEV,cAAA,8OAAC,sNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;;;;;;;;;;;;uCAnBpB,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BpC", "debugId": null}}, {"offset": {"line": 1817, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/Node/my-assistant-v3/src/components/CreateMissionDialog.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Badge } from '@/components/ui/badge';\nimport { X, Target } from 'lucide-react';\n\ninterface CreateMissionDialogProps {\n  open: boolean;\n  onOpenChange: (open: boolean) => void;\n  onSubmit: (data: { title: string; description: string; priority: string }) => void;\n}\n\nexport function CreateMissionDialog({ open, onOpenChange, onSubmit }: CreateMissionDialogProps) {\n  const [title, setTitle] = useState('');\n  const [description, setDescription] = useState('');\n  const [priority, setPriority] = useState('medium');\n  const [loading, setLoading] = useState(false);\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!title.trim() || !description.trim()) return;\n\n    setLoading(true);\n    try {\n      await onSubmit({ title: title.trim(), description: description.trim(), priority });\n      setTitle('');\n      setDescription('');\n      setPriority('medium');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const priorities = [\n    { value: 'low', label: 'Low', color: 'bg-gray-100 text-gray-800' },\n    { value: 'medium', label: 'Medium', color: 'bg-blue-100 text-blue-800' },\n    { value: 'high', label: 'High', color: 'bg-orange-100 text-orange-800' },\n    { value: 'urgent', label: 'Urgent', color: 'bg-red-100 text-red-800' },\n  ];\n\n  if (!open) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\n      <Card className=\"w-full max-w-md\">\n        <CardHeader>\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-2\">\n              <Target className=\"h-5 w-5 text-blue-600\" />\n              <CardTitle>Create New Mission</CardTitle>\n            </div>\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={() => onOpenChange(false)}\n              className=\"h-6 w-6 p-0\"\n            >\n              <X className=\"h-4 w-4\" />\n            </Button>\n          </div>\n          <CardDescription>\n            Define a new mission for your AI agent to execute autonomously\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <form onSubmit={handleSubmit} className=\"space-y-4\">\n            <div>\n              <label htmlFor=\"title\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Mission Title\n              </label>\n              <Input\n                id=\"title\"\n                value={title}\n                onChange={(e) => setTitle(e.target.value)}\n                placeholder=\"e.g., Analyze customer support tickets\"\n                required\n              />\n            </div>\n\n            <div>\n              <label htmlFor=\"description\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Description\n              </label>\n              <Textarea\n                id=\"description\"\n                value={description}\n                onChange={(e) => setDescription(e.target.value)}\n                placeholder=\"Describe what you want the AI agent to accomplish...\"\n                rows={4}\n                required\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Priority\n              </label>\n              <div className=\"flex flex-wrap gap-2\">\n                {priorities.map((p) => (\n                  <Badge\n                    key={p.value}\n                    className={`cursor-pointer transition-all ${\n                      priority === p.value\n                        ? p.color + ' ring-2 ring-blue-500'\n                        : p.color + ' opacity-60 hover:opacity-100'\n                    }`}\n                    onClick={() => setPriority(p.value)}\n                  >\n                    {p.label}\n                  </Badge>\n                ))}\n              </div>\n            </div>\n\n            <div className=\"flex space-x-2 pt-4\">\n              <Button\n                type=\"button\"\n                variant=\"outline\"\n                onClick={() => onOpenChange(false)}\n                className=\"flex-1\"\n              >\n                Cancel\n              </Button>\n              <Button\n                type=\"submit\"\n                disabled={!title.trim() || !description.trim() || loading}\n                className=\"flex-1\"\n              >\n                {loading ? 'Creating...' : 'Create Mission'}\n              </Button>\n            </div>\n          </form>\n        </CardContent>\n      </Card>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AARA;;;;;;;;;AAgBO,SAAS,oBAAoB,EAAE,IAAI,EAAE,YAAY,EAAE,QAAQ,EAA4B;IAC5F,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,YAAY,IAAI,IAAI;QAE1C,WAAW;QACX,IAAI;YACF,MAAM,SAAS;gBAAE,OAAO,MAAM,IAAI;gBAAI,aAAa,YAAY,IAAI;gBAAI;YAAS;YAChF,SAAS;YACT,eAAe;YACf,YAAY;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,aAAa;QACjB;YAAE,OAAO;YAAO,OAAO;YAAO,OAAO;QAA4B;QACjE;YAAE,OAAO;YAAU,OAAO;YAAU,OAAO;QAA4B;QACvE;YAAE,OAAO;YAAQ,OAAO;YAAQ,OAAO;QAAgC;QACvE;YAAE,OAAO;YAAU,OAAO;YAAU,OAAO;QAA0B;KACtE;IAED,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,8OAAC,gIAAA,CAAA,aAAU;;sCACT,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;;;;;;;8CAEb,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM,aAAa;oCAC5B,WAAU;8CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAGjB,8OAAC,gIAAA,CAAA,kBAAe;sCAAC;;;;;;;;;;;;8BAInB,8OAAC,gIAAA,CAAA,cAAW;8BACV,cAAA,8OAAC;wBAAK,UAAU;wBAAc,WAAU;;0CACtC,8OAAC;;kDACC,8OAAC;wCAAM,SAAQ;wCAAQ,WAAU;kDAA+C;;;;;;kDAGhF,8OAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,OAAO;wCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wCACxC,aAAY;wCACZ,QAAQ;;;;;;;;;;;;0CAIZ,8OAAC;;kDACC,8OAAC;wCAAM,SAAQ;wCAAc,WAAU;kDAA+C;;;;;;kDAGtF,8OAAC,oIAAA,CAAA,WAAQ;wCACP,IAAG;wCACH,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,aAAY;wCACZ,MAAM;wCACN,QAAQ;;;;;;;;;;;;0CAIZ,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,8OAAC;wCAAI,WAAU;kDACZ,WAAW,GAAG,CAAC,CAAC,kBACf,8OAAC,iIAAA,CAAA,QAAK;gDAEJ,WAAW,CAAC,8BAA8B,EACxC,aAAa,EAAE,KAAK,GAChB,EAAE,KAAK,GAAG,0BACV,EAAE,KAAK,GAAG,iCACd;gDACF,SAAS,IAAM,YAAY,EAAE,KAAK;0DAEjC,EAAE,KAAK;+CARH,EAAE,KAAK;;;;;;;;;;;;;;;;0CAcpB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAQ;wCACR,SAAS,IAAM,aAAa;wCAC5B,WAAU;kDACX;;;;;;kDAGD,8OAAC,kIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,UAAU,CAAC,MAAM,IAAI,MAAM,CAAC,YAAY,IAAI,MAAM;wCAClD,WAAU;kDAET,UAAU,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ3C", "debugId": null}}, {"offset": {"line": 2105, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/Node/my-assistant-v3/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { MissionDashboard } from '@/components/MissionDashboard';\nimport { ConfluencePanel } from '@/components/ConfluencePanel';\nimport { CreateMissionDialog } from '@/components/CreateMissionDialog';\nimport { Button } from '@/components/ui/button';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';\nimport { Separator } from '@/components/ui/separator';\nimport { Badge } from '@/components/ui/badge';\n// import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';\nimport { Progress } from '@/components/ui/progress';\nimport {\n  Bot,\n  FileText,\n  Target,\n  Zap,\n  Clock,\n  CheckCircle2,\n  AlertTriangle,\n  XCircle,\n  Calendar,\n  BarChart,\n  Filter,\n  Search\n} from 'lucide-react';\nimport { Input } from '@/components/ui/input';\n\ninterface Mission {\n  id: string;\n  title: string;\n  description: string;\n  status: 'pending' | 'planning' | 'executing' | 'completed' | 'failed';\n  priority: 'low' | 'medium' | 'high' | 'urgent';\n  createdAt: string;\n  updatedAt: string;\n  completedAt?: string;\n}\n\nexport default function Home() {\n  const [missions, setMissions] = useState<Mission[]>([]);\n  const [selectedMission, setSelectedMission] = useState<Mission | null>(null);\n  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);\n  const [loading, setLoading] = useState(true);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [activeFilter, setActiveFilter] = useState('all');\n\n  useEffect(() => {\n    fetchMissions();\n  }, []);\n\n  const fetchMissions = async () => {\n    try {\n      const response = await fetch('/api/missions');\n      const result = await response.json();\n      if (result.success) {\n        setMissions(result.data);\n      }\n    } catch (error) {\n      console.error('Failed to fetch missions:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCreateMission = async (missionData: { title: string; description: string; priority: string }) => {\n    try {\n      const response = await fetch('/api/missions', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify(missionData),\n      });\n\n      const result = await response.json();\n      if (result.success) {\n        setMissions(prev => [result.data, ...prev]);\n        setIsCreateDialogOpen(false);\n      }\n    } catch (error) {\n      console.error('Failed to create mission:', error);\n    }\n  };\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'completed': return <CheckCircle2 className=\"h-4 w-4 text-green-500\" />;\n      case 'executing': return <Zap className=\"h-4 w-4 text-blue-500\" />;\n      case 'planning': return <Clock className=\"h-4 w-4 text-yellow-500\" />;\n      case 'failed': return <XCircle className=\"h-4 w-4 text-red-500\" />;\n      default: return <AlertTriangle className=\"h-4 w-4 text-gray-500\" />;\n    }\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'completed': return 'bg-green-500';\n      case 'executing': return 'bg-blue-500';\n      case 'planning': return 'bg-yellow-500';\n      case 'failed': return 'bg-red-500';\n      default: return 'bg-gray-500';\n    }\n  };\n\n  const getStatusProgress = (status: string) => {\n    switch (status) {\n      case 'completed': return 100;\n      case 'executing': return 60;\n      case 'planning': return 30;\n      case 'failed': return 100;\n      default: return 10;\n    }\n  };\n\n  const getPriorityBadge = (priority: string) => {\n    switch (priority) {\n      case 'urgent':\n        return <Badge variant=\"destructive\" className=\"text-xs font-medium\">Urgent</Badge>;\n      case 'high':\n        return <Badge variant=\"outline\" className=\"bg-orange-100 text-orange-800 border-orange-200 text-xs font-medium\">High</Badge>;\n      case 'medium':\n        return <Badge variant=\"outline\" className=\"bg-blue-100 text-blue-800 border-blue-200 text-xs font-medium\">Medium</Badge>;\n      case 'low':\n        return <Badge variant=\"outline\" className=\"bg-gray-100 text-gray-800 border-gray-200 text-xs font-medium\">Low</Badge>;\n      default:\n        return <Badge variant=\"outline\" className=\"text-xs font-medium\">Unknown</Badge>;\n    }\n  };\n\n  const filteredMissions = missions.filter(mission => {\n    const matchesSearch = mission.title.toLowerCase().includes(searchQuery.toLowerCase()) ||\n                          mission.description.toLowerCase().includes(searchQuery.toLowerCase());\n\n    if (activeFilter === 'all') return matchesSearch;\n    return matchesSearch && mission.status === activeFilter;\n  });\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-background\">\n        <div className=\"text-center space-y-4\">\n          <Bot className=\"h-12 w-12 mx-auto text-primary animate-pulse\" />\n          <p className=\"text-muted-foreground\">Loading Service Management Assistant...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      {/* Header */}\n      <header className=\"sticky top-0 z-10 bg-background/95 backdrop-blur-sm border-b border-border\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-4\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"bg-primary/10 p-2 rounded-lg\">\n                <Bot className=\"h-6 w-6 text-primary\" />\n              </div>\n              <div>\n                <h1 className=\"text-xl font-bold text-foreground\">Service Management Assistant</h1>\n                <p className=\"text-xs text-muted-foreground\">AI-powered autonomous mission execution</p>\n              </div>\n            </div>\n            <Button onClick={() => setIsCreateDialogOpen(true)}>\n              <Target className=\"h-4 w-4 mr-2\" />\n              New Mission\n            </Button>\n          </div>\n        </div>\n      </header>\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n          {/* Mission List */}\n          <div className=\"lg:col-span-2 space-y-6\">\n            <Card>\n              <CardHeader className=\"pb-3\">\n                <div className=\"flex items-center justify-between\">\n                  <CardTitle className=\"text-lg flex items-center space-x-2\">\n                    <Target className=\"h-5 w-5 text-primary\" />\n                    <span>Mission Control</span>\n                  </CardTitle>\n                  <Button variant=\"outline\" size=\"sm\" className=\"h-8\">\n                    <Filter className=\"h-3.5 w-3.5 mr-1\" />\n                    Filter\n                  </Button>\n                </div>\n                <CardDescription>\n                  Manage and monitor your AI agent missions\n                </CardDescription>\n\n                <div className=\"mt-2 flex items-center gap-2\">\n                  <div className=\"relative flex-1\">\n                    <Search className=\"absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground\" />\n                    <Input\n                      placeholder=\"Search missions...\"\n                      className=\"pl-9\"\n                      value={searchQuery}\n                      onChange={(e) => setSearchQuery(e.target.value)}\n                    />\n                  </div>\n                </div>\n              </CardHeader>\n\n              <div className=\"px-6\">\n                <div className=\"grid grid-cols-5 mb-4 gap-1\">\n                  <Button variant={activeFilter === 'all' ? 'default' : 'outline'} size=\"sm\" onClick={() => setActiveFilter('all')}>All</Button>\n                  <Button variant={activeFilter === 'planning' ? 'default' : 'outline'} size=\"sm\" onClick={() => setActiveFilter('planning')}>Planning</Button>\n                  <Button variant={activeFilter === 'executing' ? 'default' : 'outline'} size=\"sm\" onClick={() => setActiveFilter('executing')}>Executing</Button>\n                  <Button variant={activeFilter === 'completed' ? 'default' : 'outline'} size=\"sm\" onClick={() => setActiveFilter('completed')}>Completed</Button>\n                  <Button variant={activeFilter === 'failed' ? 'default' : 'outline'} size=\"sm\" onClick={() => setActiveFilter('failed')}>Failed</Button>\n                </div>\n\n                <div className=\"space-y-0 mt-0\">\n                  {filteredMissions.length === 0 ? (\n                    <div className=\"text-center py-12\">\n                      <Target className=\"h-12 w-12 mx-auto text-muted-foreground mb-4\" />\n                      <p className=\"text-muted-foreground\">No missions found. Create your first mission to get started.</p>\n                    </div>\n                  ) : (\n                    <div className=\"space-y-3 pb-4\">\n                      {filteredMissions.map((mission) => (\n                        <div\n                          key={mission.id}\n                          className={`p-4 border rounded-lg cursor-pointer transition-all hover:shadow-md ${\n                            selectedMission?.id === mission.id\n                              ? 'border-primary bg-primary/5'\n                              : 'border-border hover:border-primary/30'\n                          }`}\n                          onClick={() => setSelectedMission(mission)}\n                        >\n                          <div className=\"flex items-start justify-between gap-4\">\n                            <div className=\"flex-1 space-y-2\">\n                              <div className=\"flex items-center gap-2\">\n                                {getStatusIcon(mission.status)}\n                                <h3 className=\"font-medium text-foreground\">{mission.title}</h3>\n                                {getPriorityBadge(mission.priority)}\n                              </div>\n\n                              <p className=\"text-sm text-muted-foreground line-clamp-2\">{mission.description}</p>\n\n                              <div className=\"flex items-center gap-2 text-xs text-muted-foreground\">\n                                <div className=\"flex items-center\">\n                                  <Calendar className=\"h-3 w-3 mr-1\" />\n                                  {new Date(mission.createdAt).toLocaleDateString()}\n                                </div>\n                                <Separator orientation=\"vertical\" className=\"h-3\" />\n                                <div className=\"flex items-center\">\n                                  <Clock className=\"h-3 w-3 mr-1\" />\n                                  {new Date(mission.updatedAt).toLocaleTimeString()}\n                                </div>\n                              </div>\n\n                              <div className=\"flex items-center gap-2\">\n                                <Progress value={getStatusProgress(mission.status)} className=\"h-1.5\" />\n                                <span className=\"text-xs font-medium capitalize text-muted-foreground\">{mission.status}</span>\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n                      ))}\n                    </div>\n                  )}\n                </div>\n              </div>\n            </Card>\n\n\n            {/* Mission Details */}\n            {selectedMission && (\n              <Card className=\"overflow-hidden\">\n                <CardHeader className=\"bg-muted/50 pb-3\">\n                  <div className=\"flex items-center justify-between\">\n                    <CardTitle className=\"text-lg flex items-center gap-2\">\n                      <div className={`w-2 h-2 rounded-full ${getStatusColor(selectedMission.status)}`} />\n                      <span>{selectedMission.title}</span>\n                    </CardTitle>\n                    {getPriorityBadge(selectedMission.priority)}\n                  </div>\n                  <CardDescription className=\"line-clamp-2\">\n                    {selectedMission.description}\n                  </CardDescription>\n                </CardHeader>\n                <CardContent className=\"pt-6\">\n                  <MissionDashboard mission={selectedMission} />\n                </CardContent>\n                <CardFooter className=\"bg-muted/30 border-t px-6 py-3\">\n                  <div className=\"flex items-center justify-between w-full text-xs text-muted-foreground\">\n                    <div className=\"flex items-center gap-4\">\n                      <div className=\"flex items-center\">\n                        <Calendar className=\"h-3.5 w-3.5 mr-1.5\" />\n                        Created: {new Date(selectedMission.createdAt).toLocaleDateString()}\n                      </div>\n                      <div className=\"flex items-center\">\n                        <Clock className=\"h-3.5 w-3.5 mr-1.5\" />\n                        Updated: {new Date(selectedMission.updatedAt).toLocaleDateString()}\n                      </div>\n                    </div>\n                    <div className=\"flex items-center\">\n                      <BarChart className=\"h-3.5 w-3.5 mr-1.5\" />\n                      Progress: {getStatusProgress(selectedMission.status)}%\n                    </div>\n                  </div>\n                </CardFooter>\n              </Card>\n            )}\n          </div>\n\n          {/* Confluence Panel */}\n          <div>\n            <Card className=\"h-full\">\n              <CardHeader className=\"pb-3\">\n                <CardTitle className=\"text-lg flex items-center space-x-2\">\n                  <FileText className=\"h-5 w-5 text-primary\" />\n                  <span>Knowledge Base</span>\n                </CardTitle>\n                <CardDescription>\n                  Access Confluence resources and documentation\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <ConfluencePanel />\n              </CardContent>\n            </Card>\n          </div>\n        </div>\n      </div>\n\n      {/* Create Mission Dialog */}\n      <CreateMissionDialog\n        open={isCreateDialogOpen}\n        onOpenChange={setIsCreateDialogOpen}\n        onSubmit={handleCreateMission}\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mFAAmF;AACnF;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;AA1BA;;;;;;;;;;;;;AAuCe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACvE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,IAAI,OAAO,OAAO,EAAE;gBAClB,YAAY,OAAO,IAAI;YACzB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,sBAAsB,OAAO;QACjC,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,iBAAiB;gBAC5C,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,IAAI,OAAO,OAAO,EAAE;gBAClB,YAAY,CAAA,OAAQ;wBAAC,OAAO,IAAI;2BAAK;qBAAK;gBAC1C,sBAAsB;YACxB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAa,qBAAO,8OAAC,qNAAA,CAAA,eAAY;oBAAC,WAAU;;;;;;YACjD,KAAK;gBAAa,qBAAO,8OAAC,gMAAA,CAAA,MAAG;oBAAC,WAAU;;;;;;YACxC,KAAK;gBAAY,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YACzC,KAAK;gBAAU,qBAAO,8OAAC,4MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YACzC;gBAAS,qBAAO,8OAAC,wNAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;QAC3C;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,OAAQ;YACN,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;oBAAc,WAAU;8BAAsB;;;;;;YACtE,KAAK;gBACH,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;oBAAU,WAAU;8BAAsE;;;;;;YAClH,KAAK;gBACH,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;oBAAU,WAAU;8BAAgE;;;;;;YAC5G,KAAK;gBACH,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;oBAAU,WAAU;8BAAgE;;;;;;YAC5G;gBACE,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;oBAAU,WAAU;8BAAsB;;;;;;QACpE;IACF;IAEA,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA;QACvC,MAAM,gBAAgB,QAAQ,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAC5D,QAAQ,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;QAExF,IAAI,iBAAiB,OAAO,OAAO;QACnC,OAAO,iBAAiB,QAAQ,MAAM,KAAK;IAC7C;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,gMAAA,CAAA,MAAG;wBAAC,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;IAI7C;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;;;;;;kDAEjB,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAoC;;;;;;0DAClD,8OAAC;gDAAE,WAAU;0DAAgC;;;;;;;;;;;;;;;;;;0CAGjD,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAS,IAAM,sBAAsB;;kDAC3C,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;0BAO3C,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;4CAAC,WAAU;;8DACpB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,gIAAA,CAAA,YAAS;4DAAC,WAAU;;8EACnB,8OAAC,sMAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;8EAClB,8OAAC;8EAAK;;;;;;;;;;;;sEAER,8OAAC,kIAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAU,MAAK;4DAAK,WAAU;;8EAC5C,8OAAC,sMAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;gEAAqB;;;;;;;;;;;;;8DAI3C,8OAAC,gIAAA,CAAA,kBAAe;8DAAC;;;;;;8DAIjB,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,sMAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;0EAClB,8OAAC,iIAAA,CAAA,QAAK;gEACJ,aAAY;gEACZ,WAAU;gEACV,OAAO;gEACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;sDAMtD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kIAAA,CAAA,SAAM;4DAAC,SAAS,iBAAiB,QAAQ,YAAY;4DAAW,MAAK;4DAAK,SAAS,IAAM,gBAAgB;sEAAQ;;;;;;sEAClH,8OAAC,kIAAA,CAAA,SAAM;4DAAC,SAAS,iBAAiB,aAAa,YAAY;4DAAW,MAAK;4DAAK,SAAS,IAAM,gBAAgB;sEAAa;;;;;;sEAC5H,8OAAC,kIAAA,CAAA,SAAM;4DAAC,SAAS,iBAAiB,cAAc,YAAY;4DAAW,MAAK;4DAAK,SAAS,IAAM,gBAAgB;sEAAc;;;;;;sEAC9H,8OAAC,kIAAA,CAAA,SAAM;4DAAC,SAAS,iBAAiB,cAAc,YAAY;4DAAW,MAAK;4DAAK,SAAS,IAAM,gBAAgB;sEAAc;;;;;;sEAC9H,8OAAC,kIAAA,CAAA,SAAM;4DAAC,SAAS,iBAAiB,WAAW,YAAY;4DAAW,MAAK;4DAAK,SAAS,IAAM,gBAAgB;sEAAW;;;;;;;;;;;;8DAG1H,8OAAC;oDAAI,WAAU;8DACZ,iBAAiB,MAAM,KAAK,kBAC3B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,sMAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;0EAClB,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;6EAGvC,8OAAC;wDAAI,WAAU;kEACZ,iBAAiB,GAAG,CAAC,CAAC,wBACrB,8OAAC;gEAEC,WAAW,CAAC,oEAAoE,EAC9E,iBAAiB,OAAO,QAAQ,EAAE,GAC9B,gCACA,yCACJ;gEACF,SAAS,IAAM,mBAAmB;0EAElC,cAAA,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAU;;oFACZ,cAAc,QAAQ,MAAM;kGAC7B,8OAAC;wFAAG,WAAU;kGAA+B,QAAQ,KAAK;;;;;;oFACzD,iBAAiB,QAAQ,QAAQ;;;;;;;0FAGpC,8OAAC;gFAAE,WAAU;0FAA8C,QAAQ,WAAW;;;;;;0FAE9E,8OAAC;gFAAI,WAAU;;kGACb,8OAAC;wFAAI,WAAU;;0GACb,8OAAC,0MAAA,CAAA,WAAQ;gGAAC,WAAU;;;;;;4FACnB,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB;;;;;;;kGAEjD,8OAAC,qIAAA,CAAA,YAAS;wFAAC,aAAY;wFAAW,WAAU;;;;;;kGAC5C,8OAAC;wFAAI,WAAU;;0GACb,8OAAC,oMAAA,CAAA,QAAK;gGAAC,WAAU;;;;;;4FAChB,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB;;;;;;;;;;;;;0FAInD,8OAAC;gFAAI,WAAU;;kGACb,8OAAC,oIAAA,CAAA,WAAQ;wFAAC,OAAO,kBAAkB,QAAQ,MAAM;wFAAG,WAAU;;;;;;kGAC9D,8OAAC;wFAAK,WAAU;kGAAwD,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;;;;;+DAhCvF,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;gCA8C5B,iCACC,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;;sDACd,8OAAC,gIAAA,CAAA,aAAU;4CAAC,WAAU;;8DACpB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,gIAAA,CAAA,YAAS;4DAAC,WAAU;;8EACnB,8OAAC;oEAAI,WAAW,CAAC,qBAAqB,EAAE,eAAe,gBAAgB,MAAM,GAAG;;;;;;8EAChF,8OAAC;8EAAM,gBAAgB,KAAK;;;;;;;;;;;;wDAE7B,iBAAiB,gBAAgB,QAAQ;;;;;;;8DAE5C,8OAAC,gIAAA,CAAA,kBAAe;oDAAC,WAAU;8DACxB,gBAAgB,WAAW;;;;;;;;;;;;sDAGhC,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;sDACrB,cAAA,8OAAC,sIAAA,CAAA,mBAAgB;gDAAC,SAAS;;;;;;;;;;;sDAE7B,8OAAC,gIAAA,CAAA,aAAU;4CAAC,WAAU;sDACpB,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,0MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;oEAAuB;oEACjC,IAAI,KAAK,gBAAgB,SAAS,EAAE,kBAAkB;;;;;;;0EAElE,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,oMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;oEAAuB;oEAC9B,IAAI,KAAK,gBAAgB,SAAS,EAAE,kBAAkB;;;;;;;;;;;;;kEAGpE,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,6OAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAuB;4DAChC,kBAAkB,gBAAgB,MAAM;4DAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCASjE,8OAAC;sCACC,cAAA,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,8OAAC,8MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,8OAAC;kEAAK;;;;;;;;;;;;0DAER,8OAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC,qIAAA,CAAA,kBAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ1B,8OAAC,yIAAA,CAAA,sBAAmB;gBAClB,MAAM;gBACN,cAAc;gBACd,UAAU;;;;;;;;;;;;AAIlB", "debugId": null}}]}