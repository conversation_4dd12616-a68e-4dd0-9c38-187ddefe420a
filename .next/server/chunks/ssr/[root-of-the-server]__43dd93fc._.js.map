{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/Node/my-assistant-v3/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/Node/my-assistant-v3/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/Node/my-assistant-v3/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 113, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/Node/my-assistant-v3/src/components/ui/chat-interface.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { useState, useCallback, useEffect, useRef, FormEvent } from \"react\"\nimport { ArrowDown, Paperclip, Mic, CornerDownLeft, MessageSquare, Plus, Settings, User, History, Search } from \"lucide-react\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Textarea } from \"@/components/ui/textarea\"\nimport { cn } from \"@/lib/utils\"\n\n// Auto-scroll hook\ninterface ScrollState {\n  isAtBottom: boolean\n  autoScrollEnabled: boolean\n}\n\ninterface UseAutoScrollOptions {\n  offset?: number\n  smooth?: boolean\n  content?: React.ReactNode\n}\n\nfunction useAutoScroll(options: UseAutoScrollOptions = {}) {\n  const { offset = 20, smooth = false, content } = options\n  const scrollRef = useRef<HTMLDivElement>(null)\n  const lastContentHeight = useRef(0)\n  const userHasScrolled = useRef(false)\n\n  const [scrollState, setScrollState] = useState<ScrollState>({\n    isAtBottom: true,\n    autoScrollEnabled: true,\n  })\n\n  const checkIsAtBottom = useCallback(\n    (element: HTMLElement) => {\n      const { scrollTop, scrollHeight, clientHeight } = element\n      const distanceToBottom = Math.abs(\n        scrollHeight - scrollTop - clientHeight\n      )\n      return distanceToBottom <= offset\n    },\n    [offset]\n  )\n\n  const scrollToBottom = useCallback(\n    (instant?: boolean) => {\n      if (!scrollRef.current) return\n\n      const targetScrollTop =\n        scrollRef.current.scrollHeight - scrollRef.current.clientHeight\n\n      if (instant) {\n        scrollRef.current.scrollTop = targetScrollTop\n      } else {\n        scrollRef.current.scrollTo({\n          top: targetScrollTop,\n          behavior: smooth ? \"smooth\" : \"auto\",\n        })\n      }\n\n      setScrollState({\n        isAtBottom: true,\n        autoScrollEnabled: true,\n      })\n      userHasScrolled.current = false\n    },\n    [smooth]\n  )\n\n  const handleScroll = useCallback(() => {\n    if (!scrollRef.current) return\n\n    const atBottom = checkIsAtBottom(scrollRef.current)\n\n    setScrollState((prev) => ({\n      isAtBottom: atBottom,\n      autoScrollEnabled: atBottom ? true : prev.autoScrollEnabled,\n    }))\n  }, [checkIsAtBottom])\n\n  useEffect(() => {\n    const element = scrollRef.current\n    if (!element) return\n\n    element.addEventListener(\"scroll\", handleScroll, { passive: true })\n    return () => element.removeEventListener(\"scroll\", handleScroll)\n  }, [handleScroll])\n\n  useEffect(() => {\n    const scrollElement = scrollRef.current\n    if (!scrollElement) return\n\n    const currentHeight = scrollElement.scrollHeight\n    const hasNewContent = currentHeight !== lastContentHeight.current\n\n    if (hasNewContent) {\n      if (scrollState.autoScrollEnabled) {\n        requestAnimationFrame(() => {\n          scrollToBottom(lastContentHeight.current === 0)\n        })\n      }\n      lastContentHeight.current = currentHeight\n    }\n  }, [content, scrollState.autoScrollEnabled, scrollToBottom])\n\n  const disableAutoScroll = useCallback(() => {\n    const atBottom = scrollRef.current\n      ? checkIsAtBottom(scrollRef.current)\n      : false\n\n    if (!atBottom) {\n      userHasScrolled.current = true\n      setScrollState((prev) => ({\n        ...prev,\n        autoScrollEnabled: false,\n      }))\n    }\n  }, [checkIsAtBottom])\n\n  return {\n    scrollRef,\n    isAtBottom: scrollState.isAtBottom,\n    autoScrollEnabled: scrollState.autoScrollEnabled,\n    scrollToBottom: () => scrollToBottom(false),\n    disableAutoScroll,\n  }\n}\n\n// Chat Input Component\ninterface ChatInputProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}\n\nconst ChatInput = React.forwardRef<HTMLTextAreaElement, ChatInputProps>(\n  ({ className, ...props }, ref) => (\n    <Textarea\n      autoComplete=\"off\"\n      ref={ref}\n      name=\"message\"\n      className={cn(\n        \"max-h-12 px-4 py-3 bg-background text-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 w-full rounded-md flex items-center h-16 resize-none\",\n        className,\n      )}\n      {...props}\n    />\n  ),\n)\nChatInput.displayName = \"ChatInput\"\n\n// Chat Message List Component\ninterface ChatMessageListProps extends React.HTMLAttributes<HTMLDivElement> {\n  smooth?: boolean\n}\n\nconst ChatMessageList = React.forwardRef<HTMLDivElement, ChatMessageListProps>(\n  ({ className, children, smooth = false, ...props }, _ref) => {\n    const {\n      scrollRef,\n      isAtBottom,\n      autoScrollEnabled,\n      scrollToBottom,\n      disableAutoScroll,\n    } = useAutoScroll({\n      smooth,\n      content: children,\n    })\n\n    return (\n      <div className=\"relative w-full h-full\">\n        <div\n          className={`flex flex-col w-full h-full p-4 overflow-y-auto ${className}`}\n          ref={scrollRef}\n          onWheel={disableAutoScroll}\n          onTouchMove={disableAutoScroll}\n          {...props}\n        >\n          <div className=\"flex flex-col gap-6\">{children}</div>\n        </div>\n\n        {!isAtBottom && (\n          <Button\n            onClick={() => {\n              scrollToBottom()\n            }}\n            size=\"icon\"\n            variant=\"outline\"\n            className=\"absolute bottom-2 left-1/2 transform -translate-x-1/2 inline-flex rounded-full shadow-md\"\n            aria-label=\"Scroll to bottom\"\n          >\n            <ArrowDown className=\"h-4 w-4\" />\n          </Button>\n        )}\n      </div>\n    )\n  }\n)\n\nChatMessageList.displayName = \"ChatMessageList\"\n\n// Message Interface\ninterface Message {\n  id: number\n  content: string\n  sender: \"user\" | \"ai\"\n  timestamp: Date\n}\n\ninterface Mission {\n  id: number\n  title: string\n  timestamp: Date\n  status: \"completed\" | \"in-progress\" | \"failed\"\n}\n\nexport { ChatInput, ChatMessageList, useAutoScroll, type Message, type Mission }\n"], "names": [], "mappings": ";;;;;;AAEA;AAEA;AACA;AACA;AACA;AAPA;;;;;;;;AAqBA,SAAS,cAAc,UAAgC,CAAC,CAAC;IACvD,MAAM,EAAE,SAAS,EAAE,EAAE,SAAS,KAAK,EAAE,OAAO,EAAE,GAAG;IACjD,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IACzC,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACjC,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAE/B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;QAC1D,YAAY;QACZ,mBAAmB;IACrB;IAEA,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAChC,CAAC;QACC,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,YAAY,EAAE,GAAG;QAClD,MAAM,mBAAmB,KAAK,GAAG,CAC/B,eAAe,YAAY;QAE7B,OAAO,oBAAoB;IAC7B,GACA;QAAC;KAAO;IAGV,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC/B,CAAC;QACC,IAAI,CAAC,UAAU,OAAO,EAAE;QAExB,MAAM,kBACJ,UAAU,OAAO,CAAC,YAAY,GAAG,UAAU,OAAO,CAAC,YAAY;QAEjE,IAAI,SAAS;YACX,UAAU,OAAO,CAAC,SAAS,GAAG;QAChC,OAAO;YACL,UAAU,OAAO,CAAC,QAAQ,CAAC;gBACzB,KAAK;gBACL,UAAU,SAAS,WAAW;YAChC;QACF;QAEA,eAAe;YACb,YAAY;YACZ,mBAAmB;QACrB;QACA,gBAAgB,OAAO,GAAG;IAC5B,GACA;QAAC;KAAO;IAGV,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC/B,IAAI,CAAC,UAAU,OAAO,EAAE;QAExB,MAAM,WAAW,gBAAgB,UAAU,OAAO;QAElD,eAAe,CAAC,OAAS,CAAC;gBACxB,YAAY;gBACZ,mBAAmB,WAAW,OAAO,KAAK,iBAAiB;YAC7D,CAAC;IACH,GAAG;QAAC;KAAgB;IAEpB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,UAAU,UAAU,OAAO;QACjC,IAAI,CAAC,SAAS;QAEd,QAAQ,gBAAgB,CAAC,UAAU,cAAc;YAAE,SAAS;QAAK;QACjE,OAAO,IAAM,QAAQ,mBAAmB,CAAC,UAAU;IACrD,GAAG;QAAC;KAAa;IAEjB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB,UAAU,OAAO;QACvC,IAAI,CAAC,eAAe;QAEpB,MAAM,gBAAgB,cAAc,YAAY;QAChD,MAAM,gBAAgB,kBAAkB,kBAAkB,OAAO;QAEjE,IAAI,eAAe;YACjB,IAAI,YAAY,iBAAiB,EAAE;gBACjC,sBAAsB;oBACpB,eAAe,kBAAkB,OAAO,KAAK;gBAC/C;YACF;YACA,kBAAkB,OAAO,GAAG;QAC9B;IACF,GAAG;QAAC;QAAS,YAAY,iBAAiB;QAAE;KAAe;IAE3D,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACpC,MAAM,WAAW,UAAU,OAAO,GAC9B,gBAAgB,UAAU,OAAO,IACjC;QAEJ,IAAI,CAAC,UAAU;YACb,gBAAgB,OAAO,GAAG;YAC1B,eAAe,CAAC,OAAS,CAAC;oBACxB,GAAG,IAAI;oBACP,mBAAmB;gBACrB,CAAC;QACH;IACF,GAAG;QAAC;KAAgB;IAEpB,OAAO;QACL;QACA,YAAY,YAAY,UAAU;QAClC,mBAAmB,YAAY,iBAAiB;QAChD,gBAAgB,IAAM,eAAe;QACrC;IACF;AACF;AAKA,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC,oIAAA,CAAA,WAAQ;QACP,cAAa;QACb,KAAK;QACL,MAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sOACA;QAED,GAAG,KAAK;;;;;;AAIf,UAAU,WAAW,GAAG;AAOxB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EACrC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,KAAK,EAAE,GAAG,OAAO,EAAE;IAClD,MAAM,EACJ,SAAS,EACT,UAAU,EACV,iBAAiB,EACjB,cAAc,EACd,iBAAiB,EAClB,GAAG,cAAc;QAChB;QACA,SAAS;IACX;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBACC,WAAW,CAAC,gDAAgD,EAAE,WAAW;gBACzE,KAAK;gBACL,SAAS;gBACT,aAAa;gBACZ,GAAG,KAAK;0BAET,cAAA,8OAAC;oBAAI,WAAU;8BAAuB;;;;;;;;;;;YAGvC,CAAC,4BACA,8OAAC,kIAAA,CAAA,SAAM;gBACL,SAAS;oBACP;gBACF;gBACA,MAAK;gBACL,SAAQ;gBACR,WAAU;gBACV,cAAW;0BAEX,cAAA,8OAAC,gNAAA,CAAA,YAAS;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAK/B;AAGF,gBAAgB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 299, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/Node/my-assistant-v3/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 325, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/Node/my-assistant-v3/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 422, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/Node/my-assistant-v3/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 468, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/Node/my-assistant-v3/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\n  showCloseButton?: boolean\n}) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        {showCloseButton && (\n          <DialogPrimitive.Close\n            data-slot=\"dialog-close\"\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\n          >\n            <XIcon />\n            <span className=\"sr-only\">Close</span>\n          </DialogPrimitive.Close>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,8OAAC,kKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ;IACC,qBACE,8OAAC;QAAa,aAAU;;0BACtB,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,8OAAC,kKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,8OAAC,gMAAA,CAAA,QAAK;;;;;0CACN,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 642, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/Node/my-assistant-v3/src/components/KnowledgeBaseManager.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { useState, useEffect } from \"react\"\nimport {\n  FileText,\n  Plus,\n  Search,\n  ExternalLink,\n  RefreshCw,\n  Trash2,\n  Eye,\n  Calendar,\n  Hash,\n  CheckCircle,\n  AlertCircle,\n  Clock,\n  Download,\n  Upload,\n  X\n} from \"lucide-react\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Input } from \"@/components/ui/input\"\nimport { Textarea } from \"@/components/ui/textarea\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Separator } from \"@/components/ui/separator\"\nimport { Progress } from \"@/components/ui/progress\"\nimport { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from \"@/components/ui/dialog\"\nimport { cn } from \"@/lib/utils\"\n\ninterface KnowledgeItem {\n  id: string\n  title: string\n  url: string\n  lastIndexed: Date\n  status: \"indexed\" | \"failed\" | \"pending\"\n  wordCount: number\n  tags: string[]\n  content?: string\n}\n\ninterface KnowledgeBaseManagerProps {\n  className?: string\n}\n\nconst KnowledgeBaseManager: React.FC<KnowledgeBaseManagerProps> = ({ className }) => {\n  const [knowledgeItems, setKnowledgeItems] = useState<KnowledgeItem[]>([\n    {\n      id: \"1\",\n      title: \"API Documentation - Authentication\",\n      url: \"https://confluence.company.com/api/auth\",\n      lastIndexed: new Date(Date.now() - 86400000),\n      status: \"indexed\",\n      wordCount: 1250,\n      tags: [\"API\", \"Authentication\", \"Security\"]\n    },\n    {\n      id: \"2\",\n      title: \"Service Deployment Guide\",\n      url: \"https://confluence.company.com/deployment/guide\",\n      lastIndexed: new Date(Date.now() - *********),\n      status: \"indexed\",\n      wordCount: 2100,\n      tags: [\"Deployment\", \"DevOps\", \"Guide\"]\n    },\n    {\n      id: \"3\",\n      title: \"Database Schema Documentation\",\n      url: \"https://confluence.company.com/db/schema\",\n      lastIndexed: new Date(Date.now() - *********),\n      status: \"failed\",\n      wordCount: 0,\n      tags: [\"Database\", \"Schema\"]\n    }\n  ])\n\n  const [newUrls, setNewUrls] = useState(\"\")\n  const [searchQuery, setSearchQuery] = useState(\"\")\n  const [selectedItems, setSelectedItems] = useState<string[]>([])\n  const [isIndexing, setIsIndexing] = useState(false)\n  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)\n\n  const handleAddUrls = async () => {\n    const urls = newUrls.split('\\n').filter(url => url.trim())\n    if (urls.length === 0) return\n\n    setIsIndexing(true)\n\n    urls.forEach((url, index) => {\n      const newItem: KnowledgeItem = {\n        id: `new-${Date.now()}-${index}`,\n        title: \"Loading...\",\n        url: url.trim(),\n        lastIndexed: new Date(),\n        status: \"pending\",\n        wordCount: 0,\n        tags: []\n      }\n\n      setKnowledgeItems(prev => [newItem, ...prev])\n    })\n\n    setNewUrls(\"\")\n    setIsAddDialogOpen(false)\n\n    // Simulate indexing process\n    setTimeout(() => {\n      setKnowledgeItems(prev =>\n        prev.map(item =>\n          item.status === \"pending\"\n            ? { ...item, title: `Knowledge Item ${Math.floor(Math.random() * 1000)}`, status: \"indexed\" as const, wordCount: Math.floor(Math.random() * 2000) + 500, tags: [\"New\"] }\n            : item\n        )\n      )\n      setIsIndexing(false)\n    }, 2000)\n  }\n\n  const handleReindex = (id: string) => {\n    setKnowledgeItems(prev =>\n      prev.map(item =>\n        item.id === id ? { ...item, status: \"pending\" as const, lastIndexed: new Date() } : item\n      )\n    )\n\n    // Simulate reindexing\n    setTimeout(() => {\n      setKnowledgeItems(prev =>\n        prev.map(item =>\n          item.id === id ? { ...item, status: \"indexed\" as const, wordCount: Math.floor(Math.random() * 2000) + 500 } : item\n        )\n      )\n    }, 1500)\n  }\n\n  const handleDelete = (id: string) => {\n    setKnowledgeItems(prev => prev.filter(item => item.id !== id))\n    setSelectedItems(prev => prev.filter(itemId => itemId !== id))\n  }\n\n  const handleBulkDelete = () => {\n    setKnowledgeItems(prev => prev.filter(item => !selectedItems.includes(item.id)))\n    setSelectedItems([])\n  }\n\n  const toggleSelection = (id: string) => {\n    setSelectedItems(prev =>\n      prev.includes(id) ? prev.filter(itemId => itemId !== id) : [...prev, id]\n    )\n  }\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case \"indexed\": return <CheckCircle className=\"h-4 w-4 text-green-500\" />\n      case \"failed\": return <AlertCircle className=\"h-4 w-4 text-red-500\" />\n      case \"pending\": return <Clock className=\"h-4 w-4 text-yellow-500\" />\n      default: return <AlertCircle className=\"h-4 w-4 text-gray-500\" />\n    }\n  }\n\n  const getStatusBadge = (status: string) => {\n    switch (status) {\n      case \"indexed\": return <Badge variant=\"outline\" className=\"bg-green-50 text-green-700 border-green-200\">Indexed</Badge>\n      case \"failed\": return <Badge variant=\"destructive\">Failed</Badge>\n      case \"pending\": return <Badge variant=\"outline\" className=\"bg-yellow-50 text-yellow-700 border-yellow-200\">Pending</Badge>\n      default: return <Badge variant=\"outline\">Unknown</Badge>\n    }\n  }\n\n  const filteredItems = knowledgeItems.filter(item =>\n    item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||\n    item.url.toLowerCase().includes(searchQuery.toLowerCase()) ||\n    item.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))\n  )\n\n  return (\n    <div className={cn(\"w-full h-full p-6 overflow-y-auto\", className)}>\n      {/* Header */}\n      <div className=\"flex justify-between items-center mb-6\">\n        <div>\n          <h2 className=\"text-2xl font-semibold flex items-center gap-2\">\n            <FileText className=\"h-6 w-6\" />\n            Knowledge Base\n          </h2>\n          <p className=\"text-muted-foreground mt-1\">Manage your indexed knowledge resources</p>\n        </div>\n\n        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>\n          <DialogTrigger asChild>\n            <Button className=\"gap-2\">\n              <Plus className=\"h-4 w-4\" />\n              Add Content\n            </Button>\n          </DialogTrigger>\n          <DialogContent className=\"sm:max-w-md\">\n            <DialogHeader>\n              <DialogTitle>Add Knowledge Content</DialogTitle>\n              <DialogDescription>\n                Add Confluence URLs to your knowledge base. Enter one URL per line for multiple imports.\n              </DialogDescription>\n            </DialogHeader>\n            <div className=\"space-y-4\">\n              <Textarea\n                placeholder=\"https://confluence.company.com/page1&#10;https://confluence.company.com/page2&#10;https://confluence.company.com/page3\"\n                value={newUrls}\n                onChange={(e) => setNewUrls(e.target.value)}\n                rows={6}\n                className=\"resize-none\"\n              />\n              <div className=\"flex justify-between items-center\">\n                <span className=\"text-sm text-muted-foreground\">\n                  {newUrls.split('\\n').filter(url => url.trim()).length} URLs ready to import\n                </span>\n                <div className=\"flex gap-2\">\n                  <Button variant=\"outline\" onClick={() => setIsAddDialogOpen(false)}>\n                    Cancel\n                  </Button>\n                  <Button onClick={handleAddUrls} disabled={!newUrls.trim() || isIndexing}>\n                    {isIndexing ? (\n                      <>\n                        <RefreshCw className=\"h-4 w-4 mr-2 animate-spin\" />\n                        Indexing...\n                      </>\n                    ) : (\n                      <>\n                        <Upload className=\"h-4 w-4 mr-2\" />\n                        Add URLs\n                      </>\n                    )}\n                  </Button>\n                </div>\n              </div>\n            </div>\n          </DialogContent>\n        </Dialog>\n      </div>\n\n      {/* Statistics Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6\">\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-muted-foreground\">Total Items</p>\n                <p className=\"text-2xl font-bold\">{knowledgeItems.length}</p>\n              </div>\n              <FileText className=\"h-8 w-8 text-muted-foreground\" />\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-muted-foreground\">Indexed</p>\n                <p className=\"text-2xl font-bold text-green-600\">\n                  {knowledgeItems.filter(item => item.status === \"indexed\").length}\n                </p>\n              </div>\n              <CheckCircle className=\"h-8 w-8 text-green-500\" />\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-muted-foreground\">Failed</p>\n                <p className=\"text-2xl font-bold text-red-600\">\n                  {knowledgeItems.filter(item => item.status === \"failed\").length}\n                </p>\n              </div>\n              <AlertCircle className=\"h-8 w-8 text-red-500\" />\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-muted-foreground\">Pending</p>\n                <p className=\"text-2xl font-bold text-yellow-600\">\n                  {knowledgeItems.filter(item => item.status === \"pending\").length}\n                </p>\n              </div>\n              <Clock className=\"h-8 w-8 text-yellow-500\" />\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Search and Actions */}\n      <div className=\"flex gap-4 mb-6\">\n        <div className=\"relative flex-1\">\n          <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n          <Input\n            placeholder=\"Search by title, URL, or tags...\"\n            value={searchQuery}\n            onChange={(e) => setSearchQuery(e.target.value)}\n            className=\"pl-10\"\n          />\n        </div>\n        {selectedItems.length > 0 && (\n          <div className=\"flex gap-2\">\n            <Button variant=\"outline\" size=\"sm\">\n              <RefreshCw className=\"h-4 w-4 mr-2\" />\n              Reindex Selected ({selectedItems.length})\n            </Button>\n            <Button variant=\"destructive\" size=\"sm\" onClick={handleBulkDelete}>\n              <Trash2 className=\"h-4 w-4 mr-2\" />\n              Delete Selected ({selectedItems.length})\n            </Button>\n          </div>\n        )}\n        <Button variant=\"outline\">\n          <Download className=\"h-4 w-4 mr-2\" />\n          Export\n        </Button>\n      </div>\n\n      {/* Content List */}\n      <div className=\"space-y-3\">\n        {filteredItems.length === 0 ? (\n          <div className=\"text-center py-12\">\n            <FileText className=\"h-12 w-12 mx-auto text-muted-foreground mb-4 opacity-50\" />\n            <p className=\"text-muted-foreground\">\n              {searchQuery ? \"No items match your search\" : \"No knowledge base items found. Add some content to get started.\"}\n            </p>\n          </div>\n        ) : (\n          filteredItems.map((item) => (\n            <Card key={item.id} className=\"hover:shadow-md transition-shadow\">\n              <CardContent className=\"p-4\">\n                <div className=\"flex items-start gap-4\">\n                  <input\n                    type=\"checkbox\"\n                    checked={selectedItems.includes(item.id)}\n                    onChange={() => toggleSelection(item.id)}\n                    className=\"mt-1\"\n                  />\n\n                  <div className=\"flex-1 space-y-2\">\n                    <div className=\"flex items-start justify-between\">\n                      <div className=\"flex-1\">\n                        <div className=\"flex items-center gap-2 mb-1\">\n                          {getStatusIcon(item.status)}\n                          <h4 className=\"font-medium\">{item.title}</h4>\n                          {getStatusBadge(item.status)}\n                        </div>\n                        <a\n                          href={item.url}\n                          target=\"_blank\"\n                          rel=\"noopener noreferrer\"\n                          className=\"text-sm text-blue-600 hover:underline flex items-center gap-1\"\n                        >\n                          {item.url}\n                          <ExternalLink className=\"h-3 w-3\" />\n                        </a>\n                      </div>\n\n                      <div className=\"flex gap-1\">\n                        <Button variant=\"ghost\" size=\"sm\">\n                          <Eye className=\"h-4 w-4\" />\n                        </Button>\n                        <Button variant=\"ghost\" size=\"sm\" onClick={() => handleReindex(item.id)}>\n                          <RefreshCw className=\"h-4 w-4\" />\n                        </Button>\n                        <Button variant=\"ghost\" size=\"sm\" onClick={() => handleDelete(item.id)}>\n                          <Trash2 className=\"h-4 w-4\" />\n                        </Button>\n                      </div>\n                    </div>\n\n                    <div className=\"flex items-center gap-4 text-xs text-muted-foreground\">\n                      <div className=\"flex items-center gap-1\">\n                        <Calendar className=\"h-3 w-3\" />\n                        {item.lastIndexed.toLocaleDateString()}\n                      </div>\n                      <div className=\"flex items-center gap-1\">\n                        <Hash className=\"h-3 w-3\" />\n                        {item.wordCount.toLocaleString()} words\n                      </div>\n                    </div>\n\n                    {item.tags.length > 0 && (\n                      <div className=\"flex gap-1 flex-wrap\">\n                        {item.tags.map((tag) => (\n                          <Badge key={tag} variant=\"secondary\" className=\"text-xs\">\n                            {tag}\n                          </Badge>\n                        ))}\n                      </div>\n                    )}\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          ))\n        )}\n      </div>\n\n    </div>\n  )\n}\n\nexport default KnowledgeBaseManager\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBA;AACA;AACA;AACA;AACA;AAGA;AACA;AA7BA;;;;;;;;;;;AA8CA,MAAM,uBAA4D,CAAC,EAAE,SAAS,EAAE;IAC9E,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;QACpE;YACE,IAAI;YACJ,OAAO;YACP,KAAK;YACL,aAAa,IAAI,KAAK,KAAK,GAAG,KAAK;YACnC,QAAQ;YACR,WAAW;YACX,MAAM;gBAAC;gBAAO;gBAAkB;aAAW;QAC7C;QACA;YACE,IAAI;YACJ,OAAO;YACP,KAAK;YACL,aAAa,IAAI,KAAK,KAAK,GAAG,KAAK;YACnC,QAAQ;YACR,WAAW;YACX,MAAM;gBAAC;gBAAc;gBAAU;aAAQ;QACzC;QACA;YACE,IAAI;YACJ,OAAO;YACP,KAAK;YACL,aAAa,IAAI,KAAK,KAAK,GAAG,KAAK;YACnC,QAAQ;YACR,WAAW;YACX,MAAM;gBAAC;gBAAY;aAAS;QAC9B;KACD;IAED,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC/D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,MAAM,gBAAgB;QACpB,MAAM,OAAO,QAAQ,KAAK,CAAC,MAAM,MAAM,CAAC,CAAA,MAAO,IAAI,IAAI;QACvD,IAAI,KAAK,MAAM,KAAK,GAAG;QAEvB,cAAc;QAEd,KAAK,OAAO,CAAC,CAAC,KAAK;YACjB,MAAM,UAAyB;gBAC7B,IAAI,CAAC,IAAI,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,OAAO;gBAChC,OAAO;gBACP,KAAK,IAAI,IAAI;gBACb,aAAa,IAAI;gBACjB,QAAQ;gBACR,WAAW;gBACX,MAAM,EAAE;YACV;YAEA,kBAAkB,CAAA,OAAQ;oBAAC;uBAAY;iBAAK;QAC9C;QAEA,WAAW;QACX,mBAAmB;QAEnB,4BAA4B;QAC5B,WAAW;YACT,kBAAkB,CAAA,OAChB,KAAK,GAAG,CAAC,CAAA,OACP,KAAK,MAAM,KAAK,YACZ;wBAAE,GAAG,IAAI;wBAAE,OAAO,CAAC,eAAe,EAAE,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO;wBAAE,QAAQ;wBAAoB,WAAW,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,QAAQ;wBAAK,MAAM;4BAAC;yBAAM;oBAAC,IACrK;YAGR,cAAc;QAChB,GAAG;IACL;IAEA,MAAM,gBAAgB,CAAC;QACrB,kBAAkB,CAAA,OAChB,KAAK,GAAG,CAAC,CAAA,OACP,KAAK,EAAE,KAAK,KAAK;oBAAE,GAAG,IAAI;oBAAE,QAAQ;oBAAoB,aAAa,IAAI;gBAAO,IAAI;QAIxF,sBAAsB;QACtB,WAAW;YACT,kBAAkB,CAAA,OAChB,KAAK,GAAG,CAAC,CAAA,OACP,KAAK,EAAE,KAAK,KAAK;wBAAE,GAAG,IAAI;wBAAE,QAAQ;wBAAoB,WAAW,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,QAAQ;oBAAI,IAAI;QAGpH,GAAG;IACL;IAEA,MAAM,eAAe,CAAC;QACpB,kBAAkB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QAC1D,iBAAiB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,SAAU,WAAW;IAC5D;IAEA,MAAM,mBAAmB;QACvB,kBAAkB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,OAAQ,CAAC,cAAc,QAAQ,CAAC,KAAK,EAAE;QAC7E,iBAAiB,EAAE;IACrB;IAEA,MAAM,kBAAkB,CAAC;QACvB,iBAAiB,CAAA,OACf,KAAK,QAAQ,CAAC,MAAM,KAAK,MAAM,CAAC,CAAA,SAAU,WAAW,MAAM;mBAAI;gBAAM;aAAG;IAE5E;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAW,qBAAO,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAC9C,KAAK;gBAAU,qBAAO,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAC7C,KAAK;gBAAW,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YACxC;gBAAS,qBAAO,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;QACzC;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAW,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;oBAAU,WAAU;8BAA8C;;;;;;YACxG,KAAK;gBAAU,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;8BAAc;;;;;;YACnD,KAAK;gBAAW,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;oBAAU,WAAU;8BAAiD;;;;;;YAC3G;gBAAS,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;8BAAU;;;;;;QAC3C;IACF;IAEA,MAAM,gBAAgB,eAAe,MAAM,CAAC,CAAA,OAC1C,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OACzD,KAAK,GAAG,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OACvD,KAAK,IAAI,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;IAG1E,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,qCAAqC;;0BAEtD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAGlC,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;kCAG5C,8OAAC,kIAAA,CAAA,SAAM;wBAAC,MAAM;wBAAiB,cAAc;;0CAC3C,8OAAC,kIAAA,CAAA,gBAAa;gCAAC,OAAO;0CACpB,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCAAC,WAAU;;sDAChB,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;0CAIhC,8OAAC,kIAAA,CAAA,gBAAa;gCAAC,WAAU;;kDACvB,8OAAC,kIAAA,CAAA,eAAY;;0DACX,8OAAC,kIAAA,CAAA,cAAW;0DAAC;;;;;;0DACb,8OAAC,kIAAA,CAAA,oBAAiB;0DAAC;;;;;;;;;;;;kDAIrB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oIAAA,CAAA,WAAQ;gDACP,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;gDAC1C,MAAM;gDACN,WAAU;;;;;;0DAEZ,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;;4DACb,QAAQ,KAAK,CAAC,MAAM,MAAM,CAAC,CAAA,MAAO,IAAI,IAAI,IAAI,MAAM;4DAAC;;;;;;;kEAExD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kIAAA,CAAA,SAAM;gEAAC,SAAQ;gEAAU,SAAS,IAAM,mBAAmB;0EAAQ;;;;;;0EAGpE,8OAAC,kIAAA,CAAA,SAAM;gEAAC,SAAS;gEAAe,UAAU,CAAC,QAAQ,IAAI,MAAM;0EAC1D,2BACC;;sFACE,8OAAC,gNAAA,CAAA,YAAS;4EAAC,WAAU;;;;;;wEAA8B;;iGAIrD;;sFACE,8OAAC,sMAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAarD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAA4C;;;;;;0DACzD,8OAAC;gDAAE,WAAU;0DAAsB,eAAe,MAAM;;;;;;;;;;;;kDAE1D,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAK1B,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAA4C;;;;;;0DACzD,8OAAC;gDAAE,WAAU;0DACV,eAAe,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,WAAW,MAAM;;;;;;;;;;;;kDAGpE,8OAAC,2NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAK7B,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAA4C;;;;;;0DACzD,8OAAC;gDAAE,WAAU;0DACV,eAAe,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,UAAU,MAAM;;;;;;;;;;;;kDAGnE,8OAAC,oNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAK7B,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAA4C;;;;;;0DACzD,8OAAC;gDAAE,WAAU;0DACV,eAAe,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,WAAW,MAAM;;;;;;;;;;;;kDAGpE,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOzB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC,iIAAA,CAAA,QAAK;gCACJ,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gCAC9C,WAAU;;;;;;;;;;;;oBAGb,cAAc,MAAM,GAAG,mBACtB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,MAAK;;kDAC7B,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;oCACnB,cAAc,MAAM;oCAAC;;;;;;;0CAE1C,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAc,MAAK;gCAAK,SAAS;;kDAC/C,8OAAC,0MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAiB;oCACjB,cAAc,MAAM;oCAAC;;;;;;;;;;;;;kCAI7C,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAQ;;0CACd,8OAAC,0MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAMzC,8OAAC;gBAAI,WAAU;0BACZ,cAAc,MAAM,KAAK,kBACxB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,8MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,8OAAC;4BAAE,WAAU;sCACV,cAAc,+BAA+B;;;;;;;;;;;2BAIlD,cAAc,GAAG,CAAC,CAAC,qBACjB,8OAAC,gIAAA,CAAA,OAAI;wBAAe,WAAU;kCAC5B,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,SAAS,cAAc,QAAQ,CAAC,KAAK,EAAE;wCACvC,UAAU,IAAM,gBAAgB,KAAK,EAAE;wCACvC,WAAU;;;;;;kDAGZ,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;oEACZ,cAAc,KAAK,MAAM;kFAC1B,8OAAC;wEAAG,WAAU;kFAAe,KAAK,KAAK;;;;;;oEACtC,eAAe,KAAK,MAAM;;;;;;;0EAE7B,8OAAC;gEACC,MAAM,KAAK,GAAG;gEACd,QAAO;gEACP,KAAI;gEACJ,WAAU;;oEAET,KAAK,GAAG;kFACT,8OAAC,sNAAA,CAAA,eAAY;wEAAC,WAAU;;;;;;;;;;;;;;;;;;kEAI5B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kIAAA,CAAA,SAAM;gEAAC,SAAQ;gEAAQ,MAAK;0EAC3B,cAAA,8OAAC,gMAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;;;;;;0EAEjB,8OAAC,kIAAA,CAAA,SAAM;gEAAC,SAAQ;gEAAQ,MAAK;gEAAK,SAAS,IAAM,cAAc,KAAK,EAAE;0EACpE,cAAA,8OAAC,gNAAA,CAAA,YAAS;oEAAC,WAAU;;;;;;;;;;;0EAEvB,8OAAC,kIAAA,CAAA,SAAM;gEAAC,SAAQ;gEAAQ,MAAK;gEAAK,SAAS,IAAM,aAAa,KAAK,EAAE;0EACnE,cAAA,8OAAC,0MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0DAKxB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DACnB,KAAK,WAAW,CAAC,kBAAkB;;;;;;;kEAEtC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DACf,KAAK,SAAS,CAAC,cAAc;4DAAG;;;;;;;;;;;;;4CAIpC,KAAK,IAAI,CAAC,MAAM,GAAG,mBAClB,8OAAC;gDAAI,WAAU;0DACZ,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,oBACd,8OAAC,iIAAA,CAAA,QAAK;wDAAW,SAAQ;wDAAY,WAAU;kEAC5C;uDADS;;;;;;;;;;;;;;;;;;;;;;;;;;;uBAxDf,KAAK,EAAE;;;;;;;;;;;;;;;;AAwE9B;uCAEe", "debugId": null}}, {"offset": {"line": 1649, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/Node/my-assistant-v3/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { Check, ChevronRight, Circle } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst DropdownMenu = DropdownMenuPrimitive.Root\n\nconst DropdownMenuTrigger = DropdownMenuPrimitive.Trigger\n\nconst DropdownMenuGroup = DropdownMenuPrimitive.Group\n\nconst DropdownMenuPortal = DropdownMenuPrimitive.Portal\n\nconst DropdownMenuSub = DropdownMenuPrimitive.Sub\n\nconst DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup\n\nconst DropdownMenuSubTrigger = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {\n    inset?: boolean\n  }\n>(({ className, inset, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubTrigger\n    ref={ref}\n    className={cn(\n      \"flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <ChevronRight className=\"ml-auto h-4 w-4\" />\n  </DropdownMenuPrimitive.SubTrigger>\n))\nDropdownMenuSubTrigger.displayName =\n  DropdownMenuPrimitive.SubTrigger.displayName\n\nconst DropdownMenuSubContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubContent\n    ref={ref}\n    className={cn(\n      \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuSubContent.displayName =\n  DropdownMenuPrimitive.SubContent.displayName\n\nconst DropdownMenuContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>\n>(({ className, sideOffset = 4, ...props }, ref) => (\n  <DropdownMenuPrimitive.Portal>\n    <DropdownMenuPrimitive.Content\n      ref={ref}\n      sideOffset={sideOffset}\n      className={cn(\n        \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        className\n      )}\n      {...props}\n    />\n  </DropdownMenuPrimitive.Portal>\n))\nDropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName\n\nconst DropdownMenuItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName\n\nconst DropdownMenuCheckboxItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem>\n>(({ className, children, checked, ...props }, ref) => (\n  <DropdownMenuPrimitive.CheckboxItem\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    checked={checked}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.CheckboxItem>\n))\nDropdownMenuCheckboxItem.displayName =\n  DropdownMenuPrimitive.CheckboxItem.displayName\n\nconst DropdownMenuRadioItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem>\n>(({ className, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.RadioItem\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Circle className=\"h-2 w-2 fill-current\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.RadioItem>\n))\nDropdownMenuRadioItem.displayName = DropdownMenuPrimitive.RadioItem.displayName\n\nconst DropdownMenuLabel = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Label\n    ref={ref}\n    className={cn(\n      \"px-2 py-1.5 text-sm font-semibold\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName\n\nconst DropdownMenuSeparator = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nDropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName\n\nconst DropdownMenuShortcut = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLSpanElement>) => {\n  return (\n    <span\n      className={cn(\"ml-auto text-xs tracking-widest opacity-60\", className)}\n      {...props}\n    />\n  )\n}\nDropdownMenuShortcut.displayName = \"DropdownMenuShortcut\"\n\nexport {\n  DropdownMenu,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuGroup,\n  DropdownMenuPortal,\n  DropdownMenuSub,\n  DropdownMenuSubContent,\n  DropdownMenuSubTrigger,\n  DropdownMenuRadioGroup,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,eAAe,4KAAA,CAAA,OAA0B;AAE/C,MAAM,sBAAsB,4KAAA,CAAA,UAA6B;AAEzD,MAAM,oBAAoB,4KAAA,CAAA,QAA2B;AAErD,MAAM,qBAAqB,4KAAA,CAAA,SAA4B;AAEvD,MAAM,kBAAkB,4KAAA,CAAA,MAAyB;AAEjD,MAAM,yBAAyB,4KAAA,CAAA,aAAgC;AAE/D,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAK5C,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAC3C,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wIACA,SAAS,QACT;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,sNAAA,CAAA,eAAY;gBAAC,WAAU;;;;;;;;;;;;AAG5B,uBAAuB,WAAW,GAChC,4KAAA,CAAA,aAAgC,CAAC,WAAW;AAE9C,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ybACA;QAED,GAAG,KAAK;;;;;;AAGb,uBAAuB,WAAW,GAChC,4KAAA,CAAA,aAAgC,CAAC,WAAW;AAE9C,MAAM,oCAAsB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGzC,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,8OAAC,4KAAA,CAAA,SAA4B;kBAC3B,cAAA,8OAAC,4KAAA,CAAA,UAA6B;YAC5B,KAAK;YACL,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ybACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIf,oBAAoB,WAAW,GAAG,4KAAA,CAAA,UAA6B,CAAC,WAAW;AAE3E,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAKtC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,8OAAC,4KAAA,CAAA,OAA0B;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mOACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG,4KAAA,CAAA,OAA0B,CAAC,WAAW;AAErE,MAAM,yCAA2B,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBAC7C,8OAAC,4KAAA,CAAA,eAAkC;QACjC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGpB;;;;;;;AAGL,yBAAyB,WAAW,GAClC,4KAAA,CAAA,eAAkC,CAAC,WAAW;AAEhD,MAAM,sCAAwB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG3C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,sMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGrB;;;;;;;AAGL,sBAAsB,WAAW,GAAG,4KAAA,CAAA,YAA+B,CAAC,WAAW;AAE/E,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAKvC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,8OAAC,4KAAA,CAAA,QAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qCACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,4KAAA,CAAA,QAA2B,CAAC,WAAW;AAEvE,MAAM,sCAAwB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG3C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,sBAAsB,WAAW,GAAG,4KAAA,CAAA,YAA+B,CAAC,WAAW;AAE/E,MAAM,uBAAuB,CAAC,EAC5B,SAAS,EACT,GAAG,OACmC;IACtC,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8CAA8C;QAC3D,GAAG,KAAK;;;;;;AAGf;AACA,qBAAqB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1847, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/Node/my-assistant-v3/src/components/ChatGPTInterface.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { useState, FormEvent } from \"react\"\nimport { Paperclip, Mic, CornerDownLeft, MessageSquare, Plus, Settings, User, History, Search, FileText, Database, MoreVertical, Edit2, Trash2 } from \"lucide-react\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { ChatInput, ChatMessageList, Message, Mission } from \"@/components/ui/chat-interface\"\nimport KnowledgeBaseManager from \"@/components/KnowledgeBaseManager\"\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\"\nimport { cn } from \"@/lib/utils\"\n\n// Main Chat Interface Component\nconst ChatGPTInterface: React.FC = () => {\n  const [messages, setMessages] = useState<Message[]>([\n    {\n      id: 1,\n      content: \"Hello! I'm your AI assistant. How can I help you with your mission today?\",\n      sender: \"ai\",\n      timestamp: new Date(),\n    },\n  ])\n\n  const [missions, setMissions] = useState<Mission[]>([\n    {\n      id: 1,\n      title: \"Data Analysis Project\",\n      timestamp: new Date(Date.now() - 86400000),\n      status: \"completed\",\n    },\n    {\n      id: 2,\n      title: \"Market Research\",\n      timestamp: new Date(Date.now() - *********),\n      status: \"completed\",\n    },\n    {\n      id: 3,\n      title: \"Content Strategy\",\n      timestamp: new Date(Date.now() - *********),\n      status: \"in-progress\",\n    },\n  ])\n\n  const [input, setInput] = useState(\"\")\n  const [isLoading, setIsLoading] = useState(false)\n  const [sidebarOpen, setSidebarOpen] = useState(true)\n  const [currentView, setCurrentView] = useState<\"chat\" | \"knowledge\">(\"chat\")\n  const [selectedMissionId, setSelectedMissionId] = useState<number | null>(null)\n\n  const handleSubmit = (e: FormEvent) => {\n    e.preventDefault()\n    if (!input.trim()) return\n\n    const newMessage: Message = {\n      id: messages.length + 1,\n      content: input,\n      sender: \"user\",\n      timestamp: new Date(),\n    }\n\n    setMessages((prev) => [...prev, newMessage])\n    setInput(\"\")\n    setIsLoading(true)\n\n    // Simulate AI response\n    setTimeout(() => {\n      const aiResponse: Message = {\n        id: messages.length + 2,\n        content: \"I understand your request. Let me help you with that mission. I'll analyze the requirements and provide you with a comprehensive solution.\",\n        sender: \"ai\",\n        timestamp: new Date(),\n      }\n      setMessages((prev) => [...prev, aiResponse])\n      setIsLoading(false)\n    }, 1500)\n  }\n\n  const startNewMission = () => {\n    const newMission: Mission = {\n      id: missions.length + 1,\n      title: \"New Mission\",\n      timestamp: new Date(),\n      status: \"in-progress\",\n    }\n    setMissions((prev) => [newMission, ...prev])\n    setSelectedMissionId(newMission.id)\n    setCurrentView(\"chat\")\n    setMessages([\n      {\n        id: 1,\n        content: \"Hello! I'm ready to help you with your new mission. What would you like to accomplish?\",\n        sender: \"ai\",\n        timestamp: new Date(),\n      },\n    ])\n  }\n\n  const selectMission = (missionId: number) => {\n    setSelectedMissionId(missionId)\n    setCurrentView(\"chat\")\n    // Load mission-specific messages here\n    setMessages([\n      {\n        id: 1,\n        content: `Loaded mission ${missionId}. How can I help you continue with this mission?`,\n        sender: \"ai\",\n        timestamp: new Date(),\n      },\n    ])\n  }\n\n  const renameMission = (missionId: number, newTitle: string) => {\n    setMissions(prev =>\n      prev.map(mission =>\n        mission.id === missionId ? { ...mission, title: newTitle } : mission\n      )\n    )\n  }\n\n  const deleteMission = (missionId: number) => {\n    setMissions(prev => prev.filter(mission => mission.id !== missionId))\n    if (selectedMissionId === missionId) {\n      setSelectedMissionId(null)\n      setCurrentView(\"chat\")\n    }\n  }\n\n  return (\n    <div className=\"flex h-screen bg-background text-foreground\">\n      {/* Sidebar */}\n      <div className={cn(\n        \"flex flex-col bg-muted/30 border-r border-border transition-all duration-300\",\n        sidebarOpen ? \"w-80\" : \"w-0 overflow-hidden\"\n      )}>\n        <div className=\"p-4 border-b border-border\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <h2 className=\"text-lg font-semibold\">Mission Control</h2>\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              onClick={() => setSidebarOpen(!sidebarOpen)}\n            >\n              <MessageSquare className=\"h-4 w-4\" />\n            </Button>\n          </div>\n          <div className=\"space-y-2\">\n            <Button\n              onClick={startNewMission}\n              className=\"w-full gap-2\"\n              variant=\"default\"\n            >\n              <Plus className=\"h-4 w-4\" />\n              New Mission\n            </Button>\n            <Button\n              variant={currentView === \"knowledge\" ? \"default\" : \"ghost\"}\n              className=\"w-full justify-start gap-2\"\n              onClick={() => setCurrentView(\"knowledge\")}\n            >\n              <Database className=\"h-4 w-4\" />\n              Knowledge Base\n            </Button>\n          </div>\n        </div>\n\n        <div className=\"flex-1 overflow-y-auto p-4\">\n          <div className=\"mb-4\">\n            <div className=\"relative\">\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n              <input\n                type=\"text\"\n                placeholder=\"Search missions...\"\n                className=\"w-full pl-10 pr-4 py-2 bg-background border border-border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-ring\"\n              />\n            </div>\n          </div>\n\n          <div className=\"space-y-2\">\n            <h3 className=\"text-sm font-medium text-muted-foreground mb-2\">Recent Missions</h3>\n            {missions.map((mission) => (\n              <div\n                key={mission.id}\n                className={cn(\n                  \"p-3 rounded-lg cursor-pointer border transition-colors group\",\n                  selectedMissionId === mission.id\n                    ? \"bg-primary/10 border-primary/30\"\n                    : \"bg-background hover:bg-muted/50 border-border/50\"\n                )}\n                onClick={() => selectMission(mission.id)}\n              >\n                <div className=\"flex items-start justify-between\">\n                  <div className=\"flex-1 min-w-0\">\n                    <h4 className=\"text-sm font-medium truncate\">{mission.title}</h4>\n                    <p className=\"text-xs text-muted-foreground mt-1\">\n                      {mission.timestamp.toLocaleDateString()}\n                    </p>\n                  </div>\n                  <div className=\"flex items-center gap-2\">\n                    <div className={cn(\n                      \"w-2 h-2 rounded-full\",\n                      mission.status === \"completed\" && \"bg-green-500\",\n                      mission.status === \"in-progress\" && \"bg-yellow-500\",\n                      mission.status === \"failed\" && \"bg-red-500\"\n                    )} />\n                    <DropdownMenu>\n                      <DropdownMenuTrigger asChild>\n                        <Button\n                          variant=\"ghost\"\n                          size=\"icon\"\n                          className=\"h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity\"\n                          onClick={(e) => e.stopPropagation()}\n                        >\n                          <MoreVertical className=\"h-3 w-3\" />\n                        </Button>\n                      </DropdownMenuTrigger>\n                      <DropdownMenuContent align=\"end\">\n                        <DropdownMenuItem\n                          onClick={(e) => {\n                            e.stopPropagation()\n                            const newTitle = prompt(\"Enter new mission title:\", mission.title)\n                            if (newTitle && newTitle.trim()) {\n                              renameMission(mission.id, newTitle.trim())\n                            }\n                          }}\n                        >\n                          <Edit2 className=\"h-4 w-4 mr-2\" />\n                          Rename\n                        </DropdownMenuItem>\n                        <DropdownMenuItem\n                          onClick={(e) => {\n                            e.stopPropagation()\n                            if (confirm(\"Are you sure you want to delete this mission?\")) {\n                              deleteMission(mission.id)\n                            }\n                          }}\n                          className=\"text-red-600\"\n                        >\n                          <Trash2 className=\"h-4 w-4 mr-2\" />\n                          Delete\n                        </DropdownMenuItem>\n                      </DropdownMenuContent>\n                    </DropdownMenu>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        <div className=\"p-4 border-t border-border\">\n          <div className=\"flex items-center gap-3\">\n            <div className=\"w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center\">\n              <User className=\"h-4 w-4\" />\n            </div>\n            <div className=\"flex-1 min-w-0\">\n              <p className=\"text-sm font-medium\">User</p>\n              <p className=\"text-xs text-muted-foreground\">Free Plan</p>\n            </div>\n            <Button variant=\"ghost\" size=\"icon\">\n              <Settings className=\"h-4 w-4\" />\n            </Button>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Content */}\n      <div className=\"flex-1 flex flex-col\">\n        {/* Header */}\n        <div className=\"p-4 border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center gap-3\">\n              {!sidebarOpen && (\n                <Button\n                  variant=\"ghost\"\n                  size=\"icon\"\n                  onClick={() => setSidebarOpen(true)}\n                >\n                  <MessageSquare className=\"h-4 w-4\" />\n                </Button>\n              )}\n              <div>\n                <h1 className=\"text-lg font-semibold\">\n                  {currentView === \"chat\" ? \"Current Mission\" : \"Knowledge Base\"}\n                </h1>\n                <p className=\"text-sm text-muted-foreground\">\n                  {currentView === \"chat\" ? \"AI Assistant Ready\" : \"Manage your knowledge resources\"}\n                </p>\n              </div>\n            </div>\n            <div className=\"flex items-center gap-2\">\n              <Button variant=\"ghost\" size=\"icon\">\n                <History className=\"h-4 w-4\" />\n              </Button>\n            </div>\n          </div>\n        </div>\n\n        {/* Main Content Area */}\n        <div className=\"flex-1 overflow-hidden\">\n          {currentView === \"chat\" ? (\n            <ChatMessageList>\n              {messages.map((message) => (\n                <div\n                  key={message.id}\n                  className={cn(\n                    \"flex gap-3 max-w-3xl\",\n                    message.sender === \"user\" ? \"ml-auto flex-row-reverse\" : \"\"\n                  )}\n                >\n                  <div className=\"w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center shrink-0\">\n                    {message.sender === \"user\" ? (\n                      <User className=\"h-4 w-4\" />\n                    ) : (\n                      <MessageSquare className=\"h-4 w-4\" />\n                    )}\n                  </div>\n                  <div\n                    className={cn(\n                      \"rounded-lg px-4 py-3 max-w-[80%]\",\n                      message.sender === \"user\"\n                        ? \"bg-primary text-primary-foreground\"\n                        : \"bg-muted\"\n                    )}\n                  >\n                    <p className=\"text-sm\">{message.content}</p>\n                    <p className=\"text-xs opacity-70 mt-1\">\n                      {message.timestamp.toLocaleTimeString()}\n                    </p>\n                  </div>\n                </div>\n              ))}\n\n              {isLoading && (\n                <div className=\"flex gap-3 max-w-3xl\">\n                  <div className=\"w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center shrink-0\">\n                    <MessageSquare className=\"h-4 w-4\" />\n                  </div>\n                  <div className=\"rounded-lg px-4 py-3 bg-muted\">\n                    <div className=\"flex items-center gap-1\">\n                      <div className=\"w-2 h-2 bg-current rounded-full animate-pulse\" />\n                      <div className=\"w-2 h-2 bg-current rounded-full animate-pulse delay-100\" />\n                      <div className=\"w-2 h-2 bg-current rounded-full animate-pulse delay-200\" />\n                    </div>\n                  </div>\n                </div>\n              )}\n            </ChatMessageList>\n          ) : (\n            <KnowledgeBaseManager />\n          )}\n        </div>\n\n        {/* Input - Only show for chat view */}\n        {currentView === \"chat\" && (\n          <div className=\"p-4 border-t border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\n            <form\n              onSubmit={handleSubmit}\n              className=\"relative rounded-lg border bg-background focus-within:ring-1 focus-within:ring-ring p-1 max-w-4xl mx-auto\"\n            >\n              <ChatInput\n                value={input}\n                onChange={(e) => setInput(e.target.value)}\n                placeholder=\"Describe your mission or ask for help...\"\n                className=\"min-h-12 resize-none rounded-lg bg-background border-0 p-3 shadow-none focus-visible:ring-0\"\n              />\n              <div className=\"flex items-center p-3 pt-0 justify-between\">\n                <div className=\"flex\">\n                  <Button\n                    variant=\"ghost\"\n                    size=\"icon\"\n                    type=\"button\"\n                  >\n                    <Paperclip className=\"size-4\" />\n                  </Button>\n\n                  <Button\n                    variant=\"ghost\"\n                    size=\"icon\"\n                    type=\"button\"\n                  >\n                    <Mic className=\"size-4\" />\n                  </Button>\n                </div>\n                <Button\n                  type=\"submit\"\n                  size=\"sm\"\n                  className=\"ml-auto gap-1.5\"\n                  disabled={!input.trim() || isLoading}\n                >\n                  Send Mission\n                  <CornerDownLeft className=\"size-3.5\" />\n                </Button>\n              </div>\n            </form>\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n\nexport default ChatGPTInterface\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAMA;AAdA;;;;;;;;;AAgBA,gCAAgC;AAChC,MAAM,mBAA6B;IACjC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa;QAClD;YACE,IAAI;YACJ,SAAS;YACT,QAAQ;YACR,WAAW,IAAI;QACjB;KACD;IAED,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa;QAClD;YACE,IAAI;YACJ,OAAO;YACP,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK;YACjC,QAAQ;QACV;QACA;YACE,IAAI;YACJ,OAAO;YACP,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK;YACjC,QAAQ;QACV;QACA;YACE,IAAI;YACJ,OAAO;YACP,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK;YACjC,QAAQ;QACV;KACD;IAED,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IACrE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAE1E,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,CAAC,MAAM,IAAI,IAAI;QAEnB,MAAM,aAAsB;YAC1B,IAAI,SAAS,MAAM,GAAG;YACtB,SAAS;YACT,QAAQ;YACR,WAAW,IAAI;QACjB;QAEA,YAAY,CAAC,OAAS;mBAAI;gBAAM;aAAW;QAC3C,SAAS;QACT,aAAa;QAEb,uBAAuB;QACvB,WAAW;YACT,MAAM,aAAsB;gBAC1B,IAAI,SAAS,MAAM,GAAG;gBACtB,SAAS;gBACT,QAAQ;gBACR,WAAW,IAAI;YACjB;YACA,YAAY,CAAC,OAAS;uBAAI;oBAAM;iBAAW;YAC3C,aAAa;QACf,GAAG;IACL;IAEA,MAAM,kBAAkB;QACtB,MAAM,aAAsB;YAC1B,IAAI,SAAS,MAAM,GAAG;YACtB,OAAO;YACP,WAAW,IAAI;YACf,QAAQ;QACV;QACA,YAAY,CAAC,OAAS;gBAAC;mBAAe;aAAK;QAC3C,qBAAqB,WAAW,EAAE;QAClC,eAAe;QACf,YAAY;YACV;gBACE,IAAI;gBACJ,SAAS;gBACT,QAAQ;gBACR,WAAW,IAAI;YACjB;SACD;IACH;IAEA,MAAM,gBAAgB,CAAC;QACrB,qBAAqB;QACrB,eAAe;QACf,sCAAsC;QACtC,YAAY;YACV;gBACE,IAAI;gBACJ,SAAS,CAAC,eAAe,EAAE,UAAU,gDAAgD,CAAC;gBACtF,QAAQ;gBACR,WAAW,IAAI;YACjB;SACD;IACH;IAEA,MAAM,gBAAgB,CAAC,WAAmB;QACxC,YAAY,CAAA,OACV,KAAK,GAAG,CAAC,CAAA,UACP,QAAQ,EAAE,KAAK,YAAY;oBAAE,GAAG,OAAO;oBAAE,OAAO;gBAAS,IAAI;IAGnE;IAEA,MAAM,gBAAgB,CAAC;QACrB,YAAY,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;QAC1D,IAAI,sBAAsB,WAAW;YACnC,qBAAqB;YACrB,eAAe;QACjB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,gFACA,cAAc,SAAS;;kCAEvB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAwB;;;;;;kDACtC,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,eAAe,CAAC;kDAE/B,cAAA,8OAAC,wNAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAG7B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS;wCACT,WAAU;wCACV,SAAQ;;0DAER,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAG9B,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS,gBAAgB,cAAc,YAAY;wCACnD,WAAU;wCACV,SAAS,IAAM,eAAe;;0DAE9B,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;;;;;;;;kCAMtC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,WAAU;;;;;;;;;;;;;;;;;0CAKhB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAiD;;;;;;oCAC9D,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;4CAEC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gEACA,sBAAsB,QAAQ,EAAE,GAC5B,oCACA;4CAEN,SAAS,IAAM,cAAc,QAAQ,EAAE;sDAEvC,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAgC,QAAQ,KAAK;;;;;;0EAC3D,8OAAC;gEAAE,WAAU;0EACV,QAAQ,SAAS,CAAC,kBAAkB;;;;;;;;;;;;kEAGzC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,wBACA,QAAQ,MAAM,KAAK,eAAe,gBAClC,QAAQ,MAAM,KAAK,iBAAiB,iBACpC,QAAQ,MAAM,KAAK,YAAY;;;;;;0EAEjC,8OAAC,4IAAA,CAAA,eAAY;;kFACX,8OAAC,4IAAA,CAAA,sBAAmB;wEAAC,OAAO;kFAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;4EACL,SAAQ;4EACR,MAAK;4EACL,WAAU;4EACV,SAAS,CAAC,IAAM,EAAE,eAAe;sFAEjC,cAAA,8OAAC,0NAAA,CAAA,eAAY;gFAAC,WAAU;;;;;;;;;;;;;;;;kFAG5B,8OAAC,4IAAA,CAAA,sBAAmB;wEAAC,OAAM;;0FACzB,8OAAC,4IAAA,CAAA,mBAAgB;gFACf,SAAS,CAAC;oFACR,EAAE,eAAe;oFACjB,MAAM,WAAW,OAAO,4BAA4B,QAAQ,KAAK;oFACjE,IAAI,YAAY,SAAS,IAAI,IAAI;wFAC/B,cAAc,QAAQ,EAAE,EAAE,SAAS,IAAI;oFACzC;gFACF;;kGAEA,8OAAC,kMAAA,CAAA,QAAK;wFAAC,WAAU;;;;;;oFAAiB;;;;;;;0FAGpC,8OAAC,4IAAA,CAAA,mBAAgB;gFACf,SAAS,CAAC;oFACR,EAAE,eAAe;oFACjB,IAAI,QAAQ,kDAAkD;wFAC5D,cAAc,QAAQ,EAAE;oFAC1B;gFACF;gFACA,WAAU;;kGAEV,8OAAC,0MAAA,CAAA,SAAM;wFAAC,WAAU;;;;;;oFAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2CAxDxC,QAAQ,EAAE;;;;;;;;;;;;;;;;;kCAoEvB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAElB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAAsB;;;;;;sDACnC,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;8CAE/C,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;8CAC3B,cAAA,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO5B,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;wCACZ,CAAC,6BACA,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,eAAe;sDAE9B,cAAA,8OAAC,wNAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;;;;;;sDAG7B,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DACX,gBAAgB,SAAS,oBAAoB;;;;;;8DAEhD,8OAAC;oDAAE,WAAU;8DACV,gBAAgB,SAAS,uBAAuB;;;;;;;;;;;;;;;;;;8CAIvD,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,MAAK;kDAC3B,cAAA,8OAAC,wMAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO3B,8OAAC;wBAAI,WAAU;kCACZ,gBAAgB,uBACf,8OAAC,6IAAA,CAAA,kBAAe;;gCACb,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;wCAEC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wBACA,QAAQ,MAAM,KAAK,SAAS,6BAA6B;;0DAG3D,8OAAC;gDAAI,WAAU;0DACZ,QAAQ,MAAM,KAAK,uBAClB,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;yEAEhB,8OAAC,wNAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;;;;;;0DAG7B,8OAAC;gDACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oCACA,QAAQ,MAAM,KAAK,SACf,uCACA;;kEAGN,8OAAC;wDAAE,WAAU;kEAAW,QAAQ,OAAO;;;;;;kEACvC,8OAAC;wDAAE,WAAU;kEACV,QAAQ,SAAS,CAAC,kBAAkB;;;;;;;;;;;;;uCAvBpC,QAAQ,EAAE;;;;;gCA6BlB,2BACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;;;;;;sDAE3B,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;iDAOzB,8OAAC,0IAAA,CAAA,UAAoB;;;;;;;;;;oBAKxB,gBAAgB,wBACf,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,UAAU;4BACV,WAAU;;8CAEV,8OAAC,6IAAA,CAAA,YAAS;oCACR,OAAO;oCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;oCACxC,aAAY;oCACZ,WAAU;;;;;;8CAEZ,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,MAAK;8DAEL,cAAA,8OAAC,4MAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;;;;;;8DAGvB,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,MAAK;8DAEL,cAAA,8OAAC,gMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAGnB,8OAAC,kIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,MAAK;4CACL,WAAU;4CACV,UAAU,CAAC,MAAM,IAAI,MAAM;;gDAC5B;8DAEC,8OAAC,8NAAA,CAAA,iBAAc;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS5C;uCAEe", "debugId": null}}]}