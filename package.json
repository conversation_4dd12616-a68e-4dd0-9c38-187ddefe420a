{"name": "my-assistant-v3", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:init": "tsx scripts/init-db.ts", "db:studio": "drizzle-kit studio"}, "dependencies": {"@fastify/cors": "^11.0.1", "@fastify/websocket": "^11.1.0", "@paralleldrive/cuid2": "^2.2.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@types/better-sqlite3": "^7.6.13", "@types/cheerio": "^0.22.35", "@types/uuid": "^10.0.0", "@types/ws": "^8.18.1", "axios": "^1.10.0", "better-sqlite3": "^12.2.0", "cheerio": "^1.1.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "drizzle-kit": "^0.31.4", "drizzle-orm": "^0.44.2", "fastify": "^5.4.0", "llamaindex": "^0.11.12", "lucide-react": "^0.525.0", "next": "15.3.5", "node-cache": "^5.1.2", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.1", "tsx": "^4.20.3", "uuid": "^11.1.0", "ws": "^8.18.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.5", "tailwindcss": "^4", "tw-animate-css": "^1.3.5", "typescript": "^5"}}